{"type": "page", "body": [{"type": "hidden", "id": "is_head_collapsable_id", "name": "isHeadcollapsable", "label": "是否折叠头", "value": false}, {"type": "hidden", "id": "is_hide_goods_table_id", "label": "是否显示商品搜索区", "name": "isHideGoodsTable", "value": false}, {"id": "able_commit_id", "type": "hidden", "label": "是否能提交", "name": "ableCommit", "value": true}, {"type": "hidden", "id": "is_next_order_id", "label": "是否做下一单", "name": "isNextOrder", "value": true}, {"type": "hidden", "id": "is_hide_shop_cart_id", "label": "是否显示购物车", "name": "isHideShopCart", "value": false}, {"type": "hidden", "id": "order_id_id", "label": "订单ID", "name": "orderId", "value": "${orderId}"}, {"type": "hidden", "id": "is_supper_role_id", "label": "是否有权限", "name": "supperRole", "value": false}, {"type": "hidden", "id": "is_submitSuccess", "label": "是否有权限", "name": "submitSuccess", "value": false}, {"type": "hidden", "id": "isHideFormArea", "name": "isHideFormArea", "value": false}, {"type": "form", "id": "wholesale_supermarket_form", "body": [{"type": "container", "id": "u:7526ac585973", "body": [{"type": "grid", "id": "u:220533a4202c", "themeCss": {"baseControlClassName": {"padding-and-margin:default": {"paddingTop": "var(--sizes-size-5)", "paddingRight": "var(--sizes-size-5)", "paddingBottom": "var(--sizes-size-5)", "paddingLeft": "var(--sizes-size-5)"}}}, "columns": [{"body": [{"type": "flex", "id": "u:404e901dcee5", "items": [{"type": "container", "body": [{"type": "button", "label": "${isHideFormArea ? '展开': '收起'}", "id": "u:a7ed8db0ca6f", "onEvent": {"click": {"actions": [{"componentId": "isHideFormArea", "ignoreError": false, "actionType": "setValue", "args": {"value": "${!isHideFormArea}"}}]}}, "level": "link"}], "size": "none", "style": {"position": "absolute", "display": "block", "flex": "0 0 auto", "overflowX": "visible", "zIndex": 1, "inset": "auto auto auto 0"}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:c74ae5d61751", "originPosition": "right-bottom"}, {"type": "container", "body": [{"type": "select", "label": "客户", "id": "u:a475ca83ddd2", "name": "channelStoreId", "mode": "horizontal", "multiple": false, "size": "full", "required": true, "searchable": true, "selectFirst": false, "options": [], "autoFill": {"copyname": "$label"}, "disabledOn": "${orderId || anotherOrder == 1}", "static": false, "onEvent": {"change": {"weight": 0, "actions": [{"ignoreError": false, "actionType": "reset", "args": {}, "componentId": "u:270bcdb1e5ca"}, {"ignoreError": false, "actionType": "ajax", "outputVar": "responseResult", "options": {}, "api": {"url": "/purchase-data/api/cmall/head", "method": "post", "requestAdaptor": "", "adaptor": "console.log('payload__head', payload)\nlet data = payload.data;\nlet contactsDTOListDefaultSelected = {}\nlet consigneeList = [{}]\nif (data.contactList && data.contactList.length > 0) {\n  contactsDTOListDefaultSelected = data.contactList[0]\n  consigneeList = data.contactList[0].consigneeList\n}\nreturn {\n  status: 0,\n  data: {\n    ableCreditLimitAmount: data.ableCreditLimitAmount,\n    ableCreditDay: data.ableCreditDay,\n    debtAmount: data.debtAmount,\n    deliveryAddress: data.deliveryAddress,\n    deliveryPerson: consigneeList[0].consigneeName,\n    deliveryPhone: consigneeList[0].consigneePhone,\n    contactsName: contactsDTOListDefaultSelected.contactName,\n    contactsPhone: contactsDTOListDefaultSelected.contactPhone,\n    contactList: data.contactList,\n    consigneeList: consigneeList,\n    payType: (data.options && data.options.length > 0) ? data.options[0].value : null,\n    payTypeOptions: data.options,\n    storePlatformUserId: data.storePlatformUserId,\n    creditRemind: data.creditRemind,\n    storeFreeze: data.storeFreeze,\n  }\n}", "messages": {}, "data": {"businessId": "$userBusinessId", "channelStoreId": "$channelStoreId", "orderId": "$orderId"}, "sendOn": "this.channelStoreId", "responseData": {"&": "$$"}}}, {"ignoreError": false, "actionType": "dialog", "dialog": {"type": "dialog", "title": "可用信用天数不足，不可使用账期支付", "body": [{"type": "tpl", "tpl": "<p>当前可用信用天数: ${ableCreditDay }</p>\n<p>当前可用信用额度: ${ableCreditLimitAmount }</p>", "id": "u:aa8a295b2dd5"}], "id": "u:9c1657ed557e", "actions": [{"type": "button", "actionType": "confirm", "label": "使用线下现结（Enter）", "primary": true, "id": "u:8ce3e62da222", "hotKey": "Enter"}], "showCloseButton": false, "closeOnOutside": false, "closeOnEsc": false, "showErrorMsg": true, "showLoading": true, "draggable": false, "editorSetting": {"displayName": "账期提醒"}, "actionType": "dialog"}, "expression": "${event.data.responseResult.responseData.creditRemind}"}, {"actionType": "preventDefault", "expression": "${event.data.responseResult.responseData.storeFreeze}"}, {"actionType": "dialog", "ignoreError": false, "dialog": {"type": "dialog", "title": "${event.data.selectedItems.label}", "body": [{"type": "tpl", "tpl": "<p>客户被冻结</p>", "id": "u:fc80b5a36df8"}], "id": "u:0638307d2eab", "actions": [{"type": "button", "actionType": "confirm", "label": "确定", "primary": true, "id": "u:138b340096da"}], "showCloseButton": true, "closeOnOutside": false, "closeOnEsc": false, "showErrorMsg": true, "showLoading": true, "draggable": false, "actionType": "dialog", "editorSetting": {"displayName": "客户被冻结"}}, "stopPropagation": "${event.data.responseResult.responseData.storeFreeze}", "waitForAction": true, "expression": "${event.data.responseResult.responseData.storeFreeze}"}, {"ignoreError": false, "actionType": "custom", "args": {}, "script": "\nif (event.data.responseData) {\n  event.data.responseData.channelStoreId = event.data.channelStoreId\n}\ndoAction({\n  \"actionType\": \"setValue\",\n  \"componentId\": \"wholesale_supermarket_form\",\n  \"args\": {\n    \"value\": event.data.responseData\n  }\n});\n\nevent.setData({\n  ...event.data,\n  channelStoreId: event.data.channelStoreId,\n  storePlatformUserId: event.data.storePlatformUserId\n})\n// selectedItems.label\nconsole.error(event, 'head接口这步的数据')"}, {"ignoreError": false, "actionType": "ajax", "outputVar": "cartGoodsCountResponse", "options": {}, "api": {"url": "/cart-plus/api/count", "method": "post", "requestAdaptor": "", "adaptor": "console.log('购物车的数量', payload)\nconsole.log('购物车的数量api', api)\n\nreturn {\n  status: 0,\n  data: {\n    count: payload,\n  }\n}\n", "messages": {}, "data": {"bizCode": "PARTICIPATE_VOLUME_BUSINESS", "orderId": "$orderId"}, "sendOn": "this.channelStoreId &&  this.channelStoreId != 'undefined'", "headers": {"channelStoreId": "$channelStoreId", "storePlatformUserId": "$storePlatformUserId"}}}, {"ignoreError": false, "actionType": "custom", "args": {}, "script": "console.error('count请求完的数据', event)\nlet cartGoodsCountResponse = event.data.cartGoodsCountResponse\ndoAction({\n  \"ignoreError\": false,\n  \"actionType\": \"setValue\",\n  \"componentId\": \"u:262a314ce704\",\n  \"args\": {\n    \"value\": {\n      \"channelStoreId\": event.data.channelStoreId,\n      \"initTotalGoodsCount\": \"${ cartGoodsCountResponse.count}\",\n      \"totalPriceArea\": {\n        \"totalGoodsCount\": \"${cartGoodsCountResponse.count}\"\n      }\n    }\n  }\n});\n"}, {"ignoreError": false, "actionType": "reload", "componentId": "shop_cart_id"}]}}, "autoComplete": "/purchase-data/api/sap/configSelectUnifyList?keywords=$term&configType=12&businessId=$userBusinessId&storeId=${storeId}", "defaultOpenOn": "${!orderId && anotherOrder !=1}", "popoverClassName": "kp-select-channel"}], "size": "none", "style": {"position": "static", "display": "block", "flex": "1 1 auto", "flexGrow": 1, "flexBasis": "0px"}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:b1818042d384"}, {"type": "container", "body": [{"type": "button", "label": "复制", "onEvent": {"click": {"actions": [{"ignoreError": false, "actionType": "copy", "args": {"copyFormat": "text/plain", "content": "${copyname}"}}]}}, "id": "u:e1884cd26bfb"}], "size": "none", "style": {"position": "static", "display": "block", "flex": "0 0 auto"}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:569908528ffc"}], "style": {"position": "relative", "rowGap": "10px", "columnGap": "10px", "flexWrap": "nowrap", "inset": "auto"}, "isFixedHeight": false, "isFixedWidth": false}], "id": "u:d408e54a632a", "md": 5}, {"body": [{"type": "select", "label": "结算方式", "name": "payType", "id": "pay_type_id", "mode": "horizontal", "multiple": false, "required": true, "source": "$payTypeOptions", "selectFirst": true, "onEvent": {"change": {"weight": 0, "actions": [{"componentId": "u:262a314ce704", "ignoreError": false, "actionType": "setValue", "args": {"value": {"payType": "$value"}}}]}}}], "id": "u:66b9fc964fbb"}, {"body": [{"type": "select", "label": "请货标识", "name": "deliveryType", "id": "delivery_type_id", "mode": "horizontal", "multiple": false, "required": true, "options": [{"label": "紧急请货", "value": 3}, {"label": "一般请货", "value": 4}], "selectFirst": true, "value": 3, "onEvent": {"change": {"weight": 0, "actions": [{"componentId": "u:262a314ce704", "ignoreError": false, "actionType": "setValue", "args": {"value": {"deliveryType": "$value"}}}]}}}, {"type": "hidden", "label": "暂存或者提交", "name": "shiftId", "id": "shiftId_id", "value": 1}], "id": "u:703f0fa5bf54"}], "gap": "xs"}, {"type": "wrapper", "body": [{"type": "grid", "columns": [{"body": [{"type": "static", "label": "可用信用额度", "name": "ableCreditLimitAmount", "id": "u:33a65b4e43e9", "mode": "horizontal", "quickEdit": false, "popOver": false, "copyable": false, "inline": true}], "id": "u:0b49699d9467"}, {"body": [{"type": "static", "label": "可用信用天数", "name": "ableCreditDay", "id": "u:9c008511ca0e", "mode": "horizontal", "quickEdit": false, "popOver": false, "copyable": false, "inline": true}], "id": "u:76ccec6ba208"}, {"body": [{"type": "static", "label": "欠款金额", "name": "debtAmount", "id": "u:78b3fee0e5ec", "mode": "horizontal", "quickEdit": false, "popOver": false, "copyable": false, "inline": true}], "id": "u:18c06e7d79dd", "md": 6}], "gap": "xs", "themeCss": {"baseControlClassName": {"padding-and-margin:default": {"paddingTop": "var(--sizes-size-5)", "paddingRight": "var(--sizes-size-5)", "paddingBottom": "var(--sizes-size-5)", "paddingLeft": "var(--sizes-size-5)"}}}, "id": "u:145e53c6a66e"}, {"type": "grid", "columns": [{"body": [{"type": "select", "label": "联系人", "name": "contactsName", "id": "u:3bafe35b2c62", "mode": "horizontal", "multiple": false, "required": true, "selectFirst": true, "onEvent": {"change": {"weight": 0, "actions": [{"ignoreError": false, "script": "console.error(event)\ndoAction({\n  \"componentId\": \"wholesale_supermarket_form\",\n  \"groupType\": \"component\",\n  \"actionType\": \"setValue\",\n  \"args\": {\n    \"value\": {\n      \"consigneeList\": event.data.selectedItems.consigneeList,\n      \"deliveryPerson\": event.data.selectedItems.consigneeList[0].consigneeName,\n      \"deliveryPhone\": event.data.selectedItems.consigneeList[0].consigneePhone\n    }\n  }\n})", "actionType": "custom", "args": {}}]}}, "autoFill": {"contactsPhone": "${contactPhone}"}, "source": "$contactList", "labelField": "contactName", "valueField": "contactName"}], "id": "u:587987c7866b"}, {"body": [{"type": "select", "label": "收货人", "name": "deliveryPerson", "id": "u:221fdd458f25", "multiple": false, "required": true, "source": "${consigneeList}", "labelField": "consignee<PERSON><PERSON>", "valueField": "consignee<PERSON><PERSON>", "onEvent": {"change": {"weight": 0, "actions": [{"ignoreError": false, "script": "console.error(event)\ndoAction({\n  \"componentId\": \"wholesale_supermarket_form\",\n  \"groupType\": \"component\",\n  \"actionType\": \"setValue\",\n  \"args\": {\n    \"value\": {\n      \"deliveryPhone\": event.data.selectedItems.consigneePhone\n    }\n  }\n})", "actionType": "custom"}]}}}], "id": "u:0d9392233455"}, {"body": [{"type": "static", "label": "收货人电话", "name": "deliveryPhone", "id": "u:b7898cff69e4", "mode": "horizontal", "quickEdit": false, "popOver": false, "copyable": false, "inline": true}], "id": "u:2723f55780bf"}, {"body": [], "id": "u:631e7140b5b6"}], "gap": "xs", "themeCss": {"baseControlClassName": {"padding-and-margin:default": {"paddingTop": "var(--sizes-size-5)", "paddingRight": "var(--sizes-size-5)", "paddingBottom": "var(--sizes-size-5)", "paddingLeft": "var(--sizes-size-5)"}}}, "id": "u:cf94802e5d2c"}, {"type": "grid", "columns": [{"body": [{"type": "static", "label": "送货地址", "name": "deliveryAddress", "id": "u:54647416d07c", "mode": "horizontal", "quickEdit": false, "popOver": false, "copyable": false, "inline": true}], "id": "u:5b5021acbf17", "md": 6}, {"body": [{"type": "input-text", "label": "备注", "name": "comment", "id": "u:c0f72724a96a"}], "id": "u:03d8d993af50"}], "gap": "xs", "themeCss": {"baseControlClassName": {"padding-and-margin:default": {"paddingTop": "var(--sizes-size-5)", "paddingRight": "var(--sizes-size-5)", "paddingBottom": "var(--sizes-size-5)", "paddingLeft": "var(--sizes-size-5)"}}}, "id": "u:0ed7ce3723bc"}], "style": {"position": "static", "display": "block", "flexWrap": "nowrap", "fontFamily": "", "fontSize": 12}, "id": "u:2e045aca712b", "isFixedHeight": false, "isFixedWidth": false, "hiddenOn": "${isHideFormArea}"}], "style": {"display": "block", "position": "static"}, "isFixedHeight": false, "isFixedWidth": false, "className": "custom-client-hotkey"}], "title": "", "className": "", "mode": "horizontal", "onEvent": {"submitSucc": {"weight": 0, "actions": [{"actionType": "ajax", "ignoreError": false, "outputVar": "responseResult", "options": {}, "api": {"url": "/order-plus/api/order/activate", "method": "post", "requestAdaptor": "api.config.validateStatus = (status) => {\n  return status < 1000; // 处理状态码小于1000的情况\n}\nreturn api", "adaptor": "console.error(payload, response)\nif (payload === 'success') {\n  return {\n    status: 0,\n    data: {\n      activeStatus: true,\n    },\n    msg: ''\n  }\n}\nconst traceId = response.headers['x-b3-traceid'];\nconst msg = typeof payload === 'object' ? payload.errorMessage : '操作失败，请重试'\nreturn {\n  status: -1,\n  data: {\n    activeStatus: false,\n  },\n  msg: `${msg}（${traceId}）`\n};", "messages": {}, "data": {"cartOrderId": "${event.data.result.data.cartOrderId}", "orderId": "${event.data.result.data.orderNo}", "bizCode": "PARTICIPATE_VOLUME_BUSINESS"}}}, {"actionType": "custom", "args": {}, "stopPropagation": "${!event.data.responseResult.activeStatus}", "script": "console.error(event, context, '====订单激活阻断')\ndoAction({\n  \"componentId\": \"u:294eaac97c6f\",\n  \"ignoreError\": false,\n  \"actionType\": \"enabled\"\n})\ndoAction({\n  \"componentId\": \"u:f911b743cb07\",\n  \"ignoreError\": false,\n  \"actionType\": \"enabled\"\n})\ndoAction({\n  \"actionType\": \"closeDialog\",\n  \"componentId\": \"order_submit_loading\",\n  \"ignoreError\": true\n})"}, {"actionType": "setValue", "args": {"value": {"cartListDtoList": []}}, "ignoreError": false, "componentId": "u:262a314ce704"}, {"ignoreError": false, "actionType": "reset", "componentId": "wholesale_supermarket_form"}, {"ignoreError": false, "actionType": "custom", "args": {}, "script": "doAction({\n  \"actionType\": \"setValue\",\n  \"componentId\": \"wholesale_supermarket_form\",\n  \"args\": {\n    \"value\": {\n      \"payType\": 6,\n      \"deliveryType\": 4\n    }\n  }\n});\n\nconsole.error(event, '=====1111');"}, {"actionType": "dialog", "ignoreError": false, "waitForAction": true, "dialog": {"type": "dialog", "title": "订单详情", "body": [{"type": "service", "id": "u:a8ff86174ade", "body": [{"type": "spinner", "id": "u:a45aa10c1996", "spinnerWrapClassName": "kp-order-detail-loading", "overlay": true, "tip": "订单详情加载中···", "size": "lg", "className": "kp-order-detail-loading", "body": [{"type": "button", "id": "u:80042dbd56de", "label": "导出订单", "onEvent": {"click": {"actions": [{"ignoreError": false, "outputVar": "responseResult", "actionType": "ajax", "options": {"silent": false}, "api": {"url": "/order-plus-sync/api/order/download", "method": "post", "requestAdaptor": "// 发送适配器\napi.config.validateStatus = (status) => {\n  return status < 1000; // 处理状态码小于1000的情况\n}\nreturn api", "adaptor": "let data = payload;\nif (data.downloadTaskId) {\n  return {\n    data: {\n      \n    },\n    status: 0,\n    msg:\"请到下载中心查看导出结果\"\n  }\n} else {\n  let msg = payload.errorMessage || '导出失败'\n  return {\n    data: {\n\n    },\n    status: 400,\n    msg: msg\n  }\n}", "messages": {}, "data": {"type": 68, "orderId": "$orderId", "businessId": "${userBusinessId}", "orderStatus": "ALL"}, "headers": {"bizCode": "PARTICIPATE_VOLUME_BUSINESS"}}}], "weight": 0}}, "wrapperComponent": "", "align": "right", "level": "primary"}, {"type": "button", "label": "提交", "onEvent": {"click": {"actions": [{"ignoreError": false, "outputVar": "responseResult", "actionType": "ajax", "options": {}, "api": {"url": "/purchase-data/api/cmall/orderOperate", "method": "post", "requestAdaptor": "", "adaptor": "", "messages": {}, "data": {"orderId": "$orderId", "shiftId": 1, "operateType": "ORDER_CHANGE"}}}, {"actionType": "dialog", "ignoreError": false, "dialog": {"type": "dialog", "title": "提示", "body": [{"type": "alert", "id": "u:e353ac459d55", "title": "", "body": [{"type": "tpl", "tpl": "抱歉，以下商品不符合销售标准", "wrapperComponent": "", "inline": false, "id": "u:5a6d855e22f4"}], "level": "warning", "className": "mb-3"}, {"type": "crud", "syncLocation": false, "api": {"method": "get", "url": ""}, "bulkActions": [], "itemActions": [], "columns": [{"name": "goodsNo", "label": "商品编码", "type": "text", "id": "u:d291e30dbce2", "placeholder": "-"}, {"name": "itemName", "label": "商品名称", "type": "text", "id": "u:21e4c6e55d1e", "placeholder": "-"}, {"name": "errorTypeList[0].errorMsg", "label": "备注", "type": "text", "id": "u:f24747c6e227", "placeholder": "-"}], "id": "u:0ff5f3ef3fc2", "perPageAvailable": [5, 10, 20, 50, 100], "messages": {}, "source": "${event.data.detail}"}], "id": "u:abdacf54ac98", "actions": [{"type": "button", "label": "关闭", "id": "u:fc336383f92d", "level": "default", "onEvent": {}, "actionType": "cancel"}], "showCloseButton": true, "closeOnOutside": false, "closeOnEsc": false, "showErrorMsg": true, "showLoading": true, "draggable": false, "size": "lg", "actionType": "dialog", "editorSetting": {"displayName": "价格校验提醒"}}, "expression": "${event.data.error.status === 49001}", "waitForAction": false}, {"ignoreError": false, "actionType": "closeDialog", "expression": "${event.data.responseResult.responseStatus===0}", "args": {}}, {"ignoreError": false, "actionType": "custom", "script": "/* 自定义JS使用说明：\n  * 1.动作执行函数doAction，可以执行所有类型的动作\n  * 2.通过上下文对象context可以获取当前组件实例，例如context.props可以获取该组件相关属性\n  * 3.事件对象event，在doAction之后执行event.stopPropagation();可以阻止后续动作执行\n*/\nsetTimeout(() => {\n  doAction({\n    \"actionType\": \"submit\",\n    \"ignoreError\": false,\n    \"componentId\": \"u:e4da413012b7\",\n    \"outputVar\": \"submitResult\"\n  });\n}, 1000)\n\n"}]}}, "id": "u:f31cf4845ae7", "wrapperComponent": "", "align": "right", "level": "default", "visibleOn": "${ARRAYINCLUDES(showButtons, 'SUBM<PERSON>')}", "confirmText": "是否确认提交订单?", "themeCss": {"className": {"padding-and-margin:default": {"marginLeft": "10px"}}}}, {"type": "button", "id": "u:dff56af8408c", "label": "退运费", "onEvent": {"click": {"actions": [{"ignoreError": false, "outputVar": "responseResult", "actionType": "ajax", "options": {}, "api": {"url": "/order-plus/api/order/return/apply", "method": "post", "requestAdaptor": "", "adaptor": "if (payload === 'success') {\n    return {\n        status: 0,\n        data: {},\n        msg: '退费申请成功！'\n    }\n}\nreturn payload", "messages": {}, "data": {"orderId": "$orderId", "returnReason": 7, "refundRemarks": "$refundRemarks", "returnSource": 3, "refundAmount": "$expressFee2"}}}, {"ignoreError": false, "actionType": "closeDialog"}]}}, "themeCss": {"className": {"padding-and-margin:default": {"marginLeft": "10px"}}}, "visibleOn": "${ARRAYINCLUDES(showButtons, 'RETURN_EXPRESS_FEE')}"}, {"type": "property", "id": "u:fb914f6aa711", "title": "订单信息", "className": "m-t", "items": [{"label": "销售公司", "content": "$channelStoreName", "span": 1}, {"label": "客户名", "content": "${storeName}", "span": 1}, {"label": "销售订单ID", "content": "${orderId}", "span": 1}, {"label": "建单人", "content": "${createBy}", "span": 1}, {"label": "业务日期", "content": "${gmtCreate}", "span": 1}, {"label": "业务单据来源", "content": "${bizCodeStr}", "span": 1}, {"label": "请货标识", "content": "${deliveryType}", "span": 1}, {"label": "订单状态", "content": "${orderStatus}", "span": 1}, {"label": "联系人", "content": "${cashierName}", "span": 1}, {"label": "发货实付总金额", "content": "${realPayAmount ? realPayAmount/100 : ''}", "span": 1}, {"label": "实付总金额", "content": "${payAmountStr}", "span": 1}, {"label": "运费", "content": "${expressFee}", "span": 1}, {"label": "备注", "content": "${orderRemark}", "span": 1}], "column": 4, "mode": "table"}, {"type": "crud", "id": "u:afc9e88abeb0", "title": "", "className": "m-t gj-Table-bordered", "bulkActions": [], "filter": null, "headerToolbar": [{"type": "columns-toggler", "align": "left", "draggable": true, "overlay": true, "footerBtnSize": "sm"}], "perPageAvailable": [5, 10, 20, 50, 100], "messages": {}, "syncLocation": false, "perPageField": "perPage", "pageField": "page", "columnsTogglable": true, "showHeader": true, "itemActions": [], "initFetch": "", "initFetchOn": "this.orderId", "source": "${orderDetailDTOList}", "loadDataOnce": true, "matchFunc": "", "columns": [{"type": "text", "label": "订购单行号", "name": "detailId", "id": "u:925aa17eb494", "placeholder": "-"}, {"type": "text", "label": "主推", "name": "extendMap.wholesalepushlevel", "id": "u:86844c9507b5", "placeholder": "-", "inline": true, "quickEdit": false}, {"type": "text", "label": "货品ID", "name": "goodsNo", "id": "u:c61d8c70ce72", "placeholder": "-", "inline": true, "searchable": true}, {"type": "text", "label": "商品名称", "name": "skuName", "id": "u:12ab77630f7d", "placeholder": "-", "inline": true, "searchable": true}, {"type": "text", "label": "生产厂家", "name": "producter", "id": "u:6f647f41cc6d", "placeholder": "-", "searchable": true}, {"type": "text", "label": "计量单位", "name": "goodsUnit", "id": "u:0d98b9fd854a", "placeholder": "-"}, {"label": "规格型号", "name": "jhiSpecification", "placeholder": "-", "type": "text", "id": "u:ec6d93ce35d2", "sortable": true}, {"type": "text", "label": "制剂规格", "name": "prepspec", "id": "u:0b383a0eb210", "placeholder": "-", "sortable": true}, {"type": "text", "label": "经营属性", "name": "goodsline", "id": "u:bc2e6a6b8a4d", "placeholder": "-"}, {"label": "数量", "name": "skuCount", "placeholder": "-", "type": "text", "id": "u:17607d3c5596", "sortable": true}, {"label": "实际发货数量", "name": "realSkuCount", "placeholder": "-", "type": "text", "id": "u:e87d7249f527", "sortable": true}, {"type": "text", "label": "批发价", "name": "skuPrice", "id": "u:b9fc8e8bcd8a", "placeholder": "-", "inline": true, "tpl": "${skuPrice/100}", "className": "text-right", "sortable": true}, {"type": "text", "label": "成本价", "name": "extendMap.costPrice", "id": "u:a53b116e61fb", "placeholder": "-", "inline": true, "tpl": "", "className": "text-right", "sortable": true}, {"type": "text", "label": "单价", "name": "settleSinglePrice", "id": "u:0bd185ff8ed9", "placeholder": "-", "inline": true, "className": "text-right", "sortable": true, "tpl": "${settleSinglePrice}"}, {"type": "text", "label": "金额", "name": "settlePrice", "id": "u:6457b764fbf6", "placeholder": "-", "inline": true, "tpl": "${settlePrice/100}", "className": "text-right", "sortable": true}, {"type": "text", "label": "毛利额", "name": "extendMap.grossAmount", "id": "u:871cec6d8353", "placeholder": "-", "inline": true, "tpl": "", "className": "text-right", "sortable": true}, {"type": "text", "label": "毛利率", "name": "extendMap.grossRate", "id": "u:905031b1e9e0", "placeholder": "-", "inline": true, "tpl": "", "className": "text-right", "sortable": true}, {"type": "text", "label": "有效期", "name": "extendMap.expireDate", "id": "u:7e7ed72cf42b", "placeholder": "-", "inline": true, "className": "text-right", "sortable": true, "width": 100}, {"type": "tpl", "label": "库存", "id": "u:3d77f1c9104a", "placeholder": "-", "inline": true, "tpl": "${INT(availableStock)}", "quickEdit": false, "toggled": false}, {"type": "text", "label": "最小中包装", "name": "extendMap.innerqty", "id": "u:74940d7948bb", "placeholder": "-", "inline": true, "quickEdit": false}, {"type": "mapping", "label": "类型", "name": "extendMap.selfGoodsType", "id": "u:9941bd9f9a91", "placeholder": "-", "quickEdit": false, "map": {"0": "<span class='label label-info'>普品</span>", "1": "<span class='label label-success'>赠品</span>"}}, {"type": "tpl", "label": "国药准字", "name": "extendMap.apprdocno", "id": "u:815351899388", "placeholder": "-", "quickEdit": false, "inline": true, "popOver": {"trigger": "hover", "position": "left-top", "showIcon": false, "body": [{"type": "tpl", "tpl": "${extendMap.apprdocno}", "id": "u:f2abadbe7b08"}], "id": "u:76ad84a6c304", "affixFooter": false}, "tpl": "${extendMap.apprdocno|truncate:8}"}, {"type": "each", "label": "促销", "name": "promotionLabel", "placeholder": "-", "items": {"type": "tpl", "tpl": "<span class='label label-danger m-l-sm'>${promotionLabel}</span>", "id": "u:b59c25897eb7"}, "id": "u:e5e9753a3d49", "style": {"display": "block"}, "isFixedHeight": false, "isFixedWidth": false}, {"type": "operation", "label": "状态", "buttons": [{"type": "button", "label": "查看失败原因", "onEvent": {"click": {"actions": [{"ignoreError": false, "actionType": "drawer", "drawer": {"type": "drawer", "title": "提示", "body": [{"id": "u:0724ec92a296", "type": "form", "title": "表单", "mode": "horizontal", "dsType": "api", "feat": "Insert", "body": [{"type": "tpl", "tpl": "失败原因: ${stockErrorMsg}", "inline": true, "wrapperComponent": "", "id": "u:e34e0a6c1813"}], "actions": [{"type": "button", "label": "提交", "onEvent": {"click": {"actions": [{"actionType": "submit", "componentId": "u:0724ec92a296"}]}}, "level": "primary"}], "resetAfterSubmit": true}], "id": "u:09f7349e4682", "actions": [{"type": "button", "actionType": "cancel", "label": "关闭", "id": "u:0651341a9510"}], "showCloseButton": true, "closeOnOutside": false, "closeOnEsc": false, "showErrorMsg": true, "showLoading": true, "draggable": false, "actionType": "drawer", "editorSetting": {"displayName": ""}, "size": "lg", "resizable": false}}]}}, "id": "u:760948516a7d", "level": "link", "visibleOn": "${stockErrorMsg}"}, {"type": "tpl", "id": "u:b5cf4985e248", "level": "link", "visibleOn": "${!stockErrorMsg}", "disabled": true, "tpl": "正常"}], "id": "u:e3ba62e151d8", "placeholder": "-", "fixed": "right"}], "rowClassNameExpr": "${index % 2 ? '' : 'bg-gray-100'}", "footerToolbar": [{"type": "static", "name": "desc2", "wrapperComponent": "pre", "id": "u:02aef49ef741", "className": "m-t-lg", "quickEdit": false, "popOver": false, "copyable": false, "inline": true, "align": "left"}, {"type": "pagination", "tpl": "内容", "wrapperComponent": "", "id": "u:ad366f7dbea8", "align": "right"}, {"type": "statistics", "tpl": "内容", "wrapperComponent": "", "id": "u:e5d229bf3ba9", "align": "right"}]}], "showOn": "!this.orderStatus || this.newOrderStatusCur == -1"}], "dsType": "api", "api": {"method": "get", "url": "/order-plus/api/order/detail?orderId=$orderId", "messages": {}, "requestAdaptor": "", "adaptor": "\nlet orderDetails = {};\nif (payload && payload.orderBaseDTO) {\n    orderDetails = payload.orderBaseDTO.extendParamMap;\n    orderDetails.storeName = payload.orderBaseDTO.storeName;\n    orderDetails.desc = payload.orderBaseDTO.desc;\n    orderDetails.showButtons = payload.orderBaseDTO.showButtons;\n    orderDetails.newOrderStatusCur = payload.orderBaseDTO.orderStatusCur;\n    orderDetails.expressFee2 = payload.orderBaseDTO.expressFee;\n    orderDetails.expressFee = payload.orderBaseDTO.expressFee ? payload.orderBaseDTO.expressFee / 100 : '';\n    orderDetails.refundRemarks = payload.orderBaseDTO.refundRemarks;\n    orderDetails.realPayAmount = payload.orderBaseDTO.realPayAmount;\n    if (orderDetails.expressFeeReturnResult === 'true' && orderDetails.expressFee) {\n        orderDetails.expressFee = orderDetails.expressFee + '(已退)'\n    }\n   \n}\nconsole.error(orderDetails, payload);\nreturn {\n    status: 0,\n    data: {\n        ...orderDetails,\n        orderDetailDTOList: payload.orderDetailDTOList,\n        desc2: payload.orderBaseDTO.desc\n    }\n}", "data": {"orderSource": 0, "type": 10, "orderStatusCur": 1, "pageSize": "$perPage"}, "headers": {"bizCode": "PARTICIPATE_VOLUME_BUSINESS"}, "interval": 1000, "silent": false}, "interval": 1000, "stopAutoRefreshWhen": "this.orderStatus && this.newOrderStatusCur !== -1", "onEvent": {"fetchInited": {"weight": 0, "actions": [{"componentId": "u:a32ee92c5711", "groupType": "component", "actionType": "setValue", "args": {"showLoading": "1"}, "expression": "${event.data.responseData.orderStatus && event.data.responseData.newOrderStatusCur !== -1}"}]}}, "silentPolling": true}], "id": "u:0b40f16b2f39", "actions": [{"type": "button", "actionType": "cancel", "label": "取消", "id": "u:68f4e688feda"}, {"type": "button", "actionType": "confirm", "label": "确定", "primary": true, "id": "u:e290155634e3"}], "showCloseButton": true, "closeOnOutside": false, "closeOnEsc": false, "showErrorMsg": true, "showLoading": true, "draggable": false, "editorSetting": {"displayName": "订单详情"}, "actionType": "dialog", "size": "xl", "dataMap": {"orderId": "$orderNo"}, "withDefaultData": false, "dataMapSwitch": true}, "expression": "${shiftId === 1}", "data": {"orderId": "${event.data.result.data.cartOrderId}"}}, {"actionType": "url", "ignoreError": false, "args": {"blank": false, "url": "fLsGogGMXc"}, "expression": "${OR(!isNextOrder, isNextOrder == 'false')}"}, {"componentId": "goods_search_crud_zc", "actionType": "clear", "ignoreError": false, "args": {}}, {"componentId": "shop_cart_table_id", "actionType": "clear", "groupType": "component"}, {"ignoreError": false, "actionType": "setValue", "componentId": "is_submitSuccess", "args": {"value": true}}]}, "submitFail": {"weight": 0, "actions": [{"componentId": "u:f911b743cb07", "ignoreError": false, "actionType": "enabled"}, {"componentId": "u:294eaac97c6f", "ignoreError": false, "actionType": "enabled"}, {"ignoreError": false, "actionType": "custom", "script": "console.error(event, context, '提交失败')\n\ndoAction({\n  \"ignoreError\": false,\n  \"actionType\": \"closeDialog\",\n  \"componentId\": \"order_submit_loading\"\n})\n// doAction({\n//   \"ignoreError\": false,\n//   \"actionType\": \"closeDialog\",\n//   \"componentId\": \"order_submit_loading\"\n// })", "args": {}}, {"ignoreError": false, "actionType": "dialog", "dialog": {"type": "dialog", "title": "提示", "body": [{"type": "alert", "id": "u:9393b884ba2d", "title": "", "body": [{"type": "tpl", "tpl": "抱歉，以下商品库存不足/不符合销售标准", "wrapperComponent": "", "inline": false, "id": "u:be1dc73f09aa"}], "level": "warning", "className": "mb-3"}, {"type": "crud", "syncLocation": false, "api": {"method": "get", "url": ""}, "bulkActions": [], "itemActions": [], "columns": [{"name": "goodsNo", "label": "商品编码", "type": "text", "id": "u:9b115b14844c", "placeholder": "-"}, {"name": "itemName", "label": "商品名称", "type": "text", "id": "u:6cf96364631e", "placeholder": "-", "inline": true}, {"type": "tpl", "tpl": "", "inline": true, "wrapperComponent": "", "label": "购买数量", "id": "u:308ee0a91f48", "placeholder": "-", "name": "skuCount"}, {"type": "tpl", "tpl": "", "inline": true, "wrapperComponent": "", "label": "库存", "id": "u:33146241b3ec", "placeholder": "-", "name": "buyStock"}, {"name": "errorTypeList[0].errorMsg", "label": "备注", "type": "text", "id": "u:8c3b4569d182", "placeholder": "-"}], "id": "u:ec2fb738107d", "perPageAvailable": [5, 10, 20, 50, 100], "messages": {}, "source": "${event.data.error.data.errorDetailList}"}], "id": "u:711be0f03e38", "actions": [{"type": "button", "actionType": "cancel", "label": "确认", "id": "u:b1c1f5e77576", "level": "primary", "onEvent": {"click": {"weight": 0, "actions": [{"ignoreError": false, "actionType": "ajax", "outputVar": "responseResult", "options": {}, "api": {"url": "/purchase-data/api/cmall/cart", "method": "post", "requestAdaptor": "console.log('=====> 批量删除购物车', api.context)\r\nlet data = {\r\n    handleType: 2,\r\n    businessId: api.context.userBusinessId,\r\n    cartGoodsDeleteParam: {\r\n        orderId: api.context.orderId,\r\n        delType: 5,\r\n        businessId: api.context.userBusinessId,\r\n        channelStoreId: api.context.channelStoreId,\r\n        selfGoodsType: api.context.selfGoodsType,\r\n        skuIds: api.context.event.data.error.data.errorDetailList.map(item => item.itemskuId)\r\n    }\r\n}\r\nconsole.log('=======> 批量删除购物车', data)\r\napi.data = data\r\nreturn api;", "adaptor": "console.log('=====> 批量删除购物车返回payload', payload)\r\nif (payload && payload.status == 0) {\r\n    const zanCunBtn = document.getElementsByClassName('kp_zancun_hotkey')[0]\r\n    const submitBtn = document.getElementsByClassName('submit_alt_s')[0]\r\n    const submitAndNextBtn = document.getElementsByName('submit_next_order')[0]\r\n    if (window.CLICK_ZANCUN_OR_SUBMIT == 1) {\r\n        zanCunBtn.click()\r\n    } else if (window.CLICK_ZANCUN_OR_SUBMIT == 2) {\r\n        submitBtn.click()\r\n    } else if (window.CLICK_ZANCUN_OR_SUBMIT == 3) {\r\n        submitAndNextBtn.click()\r\n    }\r\n    window.CLICK_ZANCUN_OR_SUBMIT = 0\r\n}\r\nreturn payload", "messages": {}, "data": {"&": "$$"}}}]}}}], "showCloseButton": true, "closeOnOutside": false, "closeOnEsc": false, "showErrorMsg": true, "showLoading": true, "draggable": false, "size": "lg", "actionType": "dialog", "editorSetting": {"displayName": "价格校验提醒"}, "onEvent": {"cancel": {"weight": 0, "actions": []}}}, "expression": "${event.data.error.status === 49001}"}]}, "inited": {"weight": 0, "actions": [{"actionType": "ajax", "ignoreError": false, "outputVar": "responseResult", "options": {}, "api": {"url": "/purchase-data/api/cmall/head", "method": "post", "requestAdaptor": "", "adaptor": "console.log('payload__head', payload)\nlet data = payload.data;\nreturn {\n  status: 0,\n  data: {\n    ableCreditLimitAmount: data.ableCreditLimitAmount,\n    ableCreditDay: data.ableCreditDay,\n    debtAmount: data.debtAmount,\n    deliveryAddress: data.deliveryAddress,\n    deliveryPerson: data.selectedStoreConsignee.consigneeName,\n    deliveryPhone: data.selectedStoreConsignee.consigneePhone,\n    contactsName: data.selectedStoreContact.contactName,\n    contactsPhone: data.selectedStoreContact.contactPhone,\n    contactList: data.contactList,\n    consigneeList: data.selectedStoreConsigneeList,\n    payType: (data.options && data.options.length > 0) ? data.options[0].value : null,\n    payTypeOptions: data.options,\n    storePlatformUserId: data.storePlatformUserId,\n    creditRemind: data.creditRemind,\n  }\n}", "messages": {}, "data": {"businessId": "$userBusinessId", "channelStoreId": "$channelStoreId", "orderId": "$orderId"}, "sendOn": "this.channelStoreId", "responseData": {"&": "$$"}}}, {"actionType": "dialog", "ignoreError": false, "dialog": {"type": "dialog", "title": "可用信用天数不足，不可使用账期支付", "body": [{"type": "tpl", "tpl": "<p>当前可用信用天数: ${ableCreditDay }</p>\n<p>当前可用信用额度: ${ableCreditLimitAmount }</p>", "id": "u:c73aa3070fb3"}], "id": "u:551720fce157", "actions": [{"type": "button", "actionType": "confirm", "label": "使用线下现结（Enter）", "primary": true, "id": "u:43f1dca937bd", "hotKey": "Enter"}], "showCloseButton": false, "closeOnOutside": false, "closeOnEsc": false, "showErrorMsg": true, "showLoading": true, "draggable": false, "editorSetting": {"displayName": "账期提醒"}, "actionType": "dialog"}, "expression": "${event.data.responseResult.responseData.creditRemind}"}, {"ignoreError": false, "actionType": "custom", "script": "doAction({\n  \"actionType\": \"setValue\",\n  \"componentId\": \"wholesale_supermarket_form\",\n  \"args\": {\n    \"value\": event.data.responseData\n  }\n});\n\nevent.setData({\n  ...event.data,\n  channelStoreId: event.data.channelStoreId,\n  storePlatformUserId: event.data.storePlatformUserId\n})\n// selectedItems.label\nconsole.error(event, 'head接口这步的数据')", "args": {}}, {"ignoreError": false, "actionType": "ajax", "outputVar": "cartGoodsCountResponse", "options": {}, "api": {"url": "/cart-plus/api/count", "method": "post", "requestAdaptor": "", "adaptor": "console.log('购物车的数量', payload)\nconsole.log('购物车的数量api', api)\n\nreturn {\n  status: 0,\n  data: {\n    count: payload,\n  }\n}\n", "messages": {}, "data": {"bizCode": "PARTICIPATE_VOLUME_BUSINESS", "orderId": "$orderId"}, "sendOn": "this.channelStoreId &&  this.channelStoreId != 'undefined'", "headers": {"channelStoreId": "$channelStoreId", "storePlatformUserId": "$storePlatformUserId"}}}, {"ignoreError": false, "actionType": "custom", "args": {}, "script": "console.error('count请求完的数据', event)\nlet cartGoodsCountResponse = event.data.cartGoodsCountResponse\ndoAction({\n  \"ignoreError\": false,\n  \"actionType\": \"setValue\",\n  \"componentId\": \"u:262a314ce704\",\n  \"args\": {\n    \"value\": {\n      \"channelStoreId\": event.data.channelStoreId,\n      \"initTotalGoodsCount\": \"${ cartGoodsCountResponse.count}\",\n      \"totalPriceArea\": {\n        \"totalGoodsCount\": \"${cartGoodsCountResponse.count}\"\n      }\n    }\n  }\n});\n"}, {"ignoreError": false, "actionType": "reload", "componentId": "shop_cart_id"}]}}, "dsType": "api", "feat": "Edit", "actions": [], "resetAfterSubmit": false, "wrapWithPanel": false, "affixFooter": true, "debug": false, "preventEnterSubmit": true, "initApi": {"method": "get", "url": "/purchase-data/api/cmall/refreshCartPriceByOrder?orderId=${orderId}", "requestAdaptor": "", "adaptor": "if (payload.status !== 0) return payload;\nlet data = payload.data;\nlet channelStoreId = data.storeId;\nlet orderId = data.orderId;\nlet storeId = data.storeId;\nlet ableCmall = data.ableCmall;\n\nresult = {\n    data: {\n        channelStoreId,\n        orderId,\n        storeId,\n        editOrderNo: ableCmall ? orderId : '',\n        ableCmall\n    },\n    status: 0\n}\n\nconsole.log('init result', result)\nreturn result\n", "messages": {}, "sendOn": "this.orderId&&this.orderStatusType", "headers": {"bizCode": "PARTICIPATE_VOLUME_BUSINESS"}}, "api": {"url": "/purchase-data/api/cmall/createOrder", "method": "post", "requestAdaptor": "console.log('提交订单api', api)\nlet currentContext = api.context\n\nlet skuDTOList = api.data.cartListDtoList.map(row => {\n  return {\n    \"count\": row.count,\n    \"goodsName\": row.goodsName,\n    \"goodsNo\": row.goodsNo,\n    \"settleprice\": row.settleprice,\n    \"selfGoodsType\": row.selfGoodsType,\n    \"payAmount\": row.payAmount,\n    \"itemId\": row.itemId,\n    \"extendMap\": row.extendMap,\n    promotionIds: row.promotionIds,\n    giftByGoodsNo: row.giftByGoodsNo,\n    giftPromotionId: row.giftPromotionId\n  }\n})\n\napi.data = {\n  \"businessId\": currentContext.userBusinessId,\n  \"channelStoreId\": currentContext.channelStoreId,\n  \"comment\": currentContext.comment,\n  \"shiftId\": currentContext.shiftId,\n  \"contactsDTOList\": [\n    {\n      \"contactsName\": currentContext.contactsName,\n      \"contactsPhone\": currentContext.contactsPhone,\n      \"deliveryPerson\": currentContext.deliveryPerson,\n      \"deliveryPhone\": currentContext.deliveryPhone\n    }\n  ],\n  \"deliveryAddress\": currentContext.deliveryAddress,\n  \"payAmount\": currentContext.payAmount,\n  \"payType\": currentContext.payType,\n  \"deliveryType\": currentContext.deliveryType,\n  \"skuDTOList\": skuDTOList,\n  \"extendParamMap\": api.data.topExtendParamMap,\n  \"thirdPartyOrderNo\": api.data.thirdPartyOrderNo,\n  \"ableCmall\": currentContext.ableCmall,\n  \"editOrderNo\": currentContext.editOrderNo,\n  \"orderId\": currentContext.orderId\n}\nreturn api", "adaptor": "console.log('提交订单payload', payload)\nif (payload && payload.status === 49001) {\n  return {\n    ...payload,\n    data: {\n      errorDetailList: payload.msg ? JSON.parse(payload.msg) : []\n    },\n    msg: '提交失败~'\n  }\n}\n\nreturn payload", "messages": {"success": ""}, "silent": false, "data": {"&": "$$", "cartListDtoList": "$cartListDtoList", "thirdPartyOrderNo": "$orderId", "topExtendParamMap": "$topExtendParamMap"}, "headers": {"bizCode": "PARTICIPATE_VOLUME_BUSINESS", "bizFunction": "createOrder"}}, "labelAlign": "left"}, {"type": "flex", "id": "u:9fd0bab20e7d", "items": [{"type": "container", "body": [], "size": "none", "style": {"position": "static", "display": "block", "flex": "1 1 auto", "flexGrow": 1, "flexBasis": "0px"}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:de5d62793d74"}], "style": {"position": "relative", "rowGap": "10px", "columnGap": "10px", "height": "20px", "overflowY": "visible", "flexWrap": "nowrap", "inset": "auto"}, "isFixedHeight": true, "isFixedWidth": false, "themeCss": {"baseControlClassName": {"background:default": "#eff0f4", "padding-and-margin:default": {"margin": "0 -0.75rem 1.25rem"}}}}, {"type": "fieldSet", "id": "u:0f917fdd1d81", "body": [{"type": "crud", "id": "goods_search_crud_zc", "syncLocation": false, "api": {"method": "post", "url": "/purchase-data/api/sap/storeGoodsList?channelStoreId=$channelStoreId", "messages": {}, "requestAdaptor": "if (api.data.keywords) {\n  let keywords = api.data.keywords.trim()\n  if (keywords == '*') {\n    api.data.keywords = null\n  }\n  return api\n} else {\n\n  return {\n    // 模拟 http 请求返回\n    ...api,\n    mockResponse: {\n      status: 200, // http 返回状态\n      data: {\n        status: 0,\n        data: {\n          items: []\n        },\n        msg: ''\n      },\n      msg: ''\n    }\n  };\n}\n\n", "adaptor": "// let rows = payload.data.rows\n// if (rows) {\n//   rows = rows.map((row, index) => {\n//     if (index % 3 == 0) {\n//       row = {\n//         ...row,\n//         \"labelTitle\": \"满赠,满减\",\n//         \"promoDTOList\": [\n//           {\n//             \"labelTitle\": \"满赠\",\n//             \"ruleDesc\": \"满5件得(999)复方醋酸地塞米松乳膏\",\n//             \"newPLabelInfo\": {\n//               \"promotionId\": 1,\n//               \"promotionType\": 5,\n//               \"promotionWay\": 10\n//             }\n//           },\n//           {\n//             \"newPLabelInfo\": {\n//               \"promotionId\": 2,\n//               \"promotionType\": 10,\n//               \"promotionWay\": 10\n//             },\n//             \"labelTitle\": \"满减\",\n//             \"ruleDesc\": \"满2件得(999)减100\"\n//           }\n//         ]\n//       }\n//     }\n//     return row\n//   }\n//   )\n// } else {\n//   rows = []\n// }\n// payload.data.rows = rows\npayload.data.rows = payload.data.rows ? payload.data.rows : []\n\npayload.data.rows = payload.data.rows.map(row => {\n  if (row.promoDTOList) {\n    row.promoDTOList = row.promoDTOList.map(promo => {\n      promo.disabled = true\n      return promo\n    })\n  }\n  row.purchase_quantity = ''\n  return row\n})\n\nwindow.selectedRowIndex_kpzs = -1;\nwindow.selectedRowCarIndex_kpzs = -1;\nconst clearJiaGouModiRow = document.querySelector('.goods_jiagou_hotkey_clear_zc')\nclearJiaGouModiRow && clearJiaGouModiRow.click();\nconsole.log('商品所说', payload)\nreturn payload", "responseData": {"&": "$$"}, "data": {"ableCompleteData": true, "businessId": "$userBusinessId", "keywords": "$search_goods_name", "page": "$page", "perPage": "$perPage", "channelStoreId": "$channelStoreId", "orderId": "$orderId"}, "sendOn": "this.channelStoreId", "headers": {"bizCode": "PARTICIPATE_VOLUME_BUSINESS", "bizFunction": "storeGoodsList"}}, "columns": [{"label": "主推", "id": "u:02179d54c42d", "placeholder": "-", "fixed": "left", "inline": true, "type": "tpl", "quickEdit": false, "tpl": "<strong><span style=\"color: rgb(224, 62, 45);\">${wholesalepushlevel}</span></strong>", "name": "wholesalepushlevel"}, {"type": "tpl", "id": "u:4aa9ddbaaf1e", "placeholder": "-", "label": "序号", "fixed": "left", "name": "num", "tpl": "<%=data.index+1%>", "badge": {"mode": "text", "text": "锁价品", "visibleOn": "${goodsMold === 2}", "position": "top-right", "offset": [0, -10]}, "width": 60, "wrapperCustomStyle": {"width": "20px", "display": "inline-block"}}, {"id": "u:385c37375b1c", "type": "text", "name": "${goodsName}", "label": "商品", "placeholder": "-", "copyable": true, "popOver": {"type": "panel", "title": "${goodsName}", "body": [{"type": "image", "id": "u:d4f5a9125719", "enlargeAble": true, "maxScale": 200, "minScale": 50, "position": "right-top", "name": "picUrl", "style": {"display": "inline-block"}}], "id": "u:b7ead3824aa1", "affixFooter": false}, "inline": true, "quickEdit": false, "fixed": "left", "width": 140}, {"type": "text", "name": "manufacturer", "label": "厂家", "id": "u:b24eb1b26309", "placeholder": "-", "width": 140, "fixed": "left", "copyable": true, "inline": true, "quickEdit": false, "popOver": false}, {"type": "container", "name": "regulatoryCode", "label": "追溯码", "id": "u:6fe320052053", "placeholder": "-", "className": "kp-godds-first-td", "body": [{"type": "tpl", "id": "u:0613773f2500", "quickEdit": false, "style": {"text-align": "center"}, "tpl": "追溯码", "visibleOn": "${regulatoryCode == '是'}", "wrapperCustomStyle": {"background": "#E60036", "padding": "2px 5px", "color": "white", "display": "inline-block", "border-radius": "4px", "margin-left": "10px", "font-size": "12px"}}], "size": "none", "wrapperBody": false, "width": 100}, {"name": "wholesalePrice", "label": "批发价", "id": "u:2034c2070a1b", "placeholder": "-", "inline": true, "type": "text", "tpl": "${priceStatus === 0 ?wholesalePrice : '价格作废'}", "className": "kp-godds-first-td"}, {"label": "成本价", "id": "u:6b25e57ffa8c", "placeholder": "-", "inline": true, "type": "text", "name": "costPrice", "quickEdit": false, "tpl": "${supperRole ? costPrice:'-'}"}, {"id": "u:5f8d30392dc6", "label": "上次销价", "placeholder": "-", "name": "lastSalePrice", "type": "text", "inline": true, "quickEdit": false}, {"label": "零售价", "id": "u:5520e8541b03", "placeholder": "-", "type": "text", "name": "advicePrice", "inline": true}, {"label": "库存", "id": "u:54dd3cca75ff", "placeholder": "-", "inline": true, "type": "tpl", "name": "availableStock", "quickEdit": false, "tpl": "${INT(availableStock)}"}, {"label": "有效期", "id": "u:527d419caf6e", "placeholder": "-", "type": "text", "name": "recentlyDate", "quickEdit": false, "inline": true, "width": 100}, {"label": "促销标签", "id": "u:4a9f364fdbc1", "placeholder": "-", "type": "text", "name": "labelTitle", "quickEdit": false, "inline": true}, {"label": "促销详情", "id": "u:d6e9ecf2251c", "placeholder": "-", "type": "container", "name": "promoSelect", "width": 180, "body": [{"type": "select", "label": "", "id": "u:3f53e50dff31", "placeholder": "-", "name": "promoSelect", "staticOn": "${!ablePromo}", "searchable": false, "source": "$promoDTOList", "multiple": false, "valueField": "labelId", "labelField": "labelTitle", "removable": false, "clearable": true, "static": true, "joinValues": false, "disabled": "true", "disabledOn": "${!ablePromo}", "visibleOn": "${promoDTOList.length > 1}", "selectMode": "table", "columns": [{"name": "labelTitle", "label": ""}, {"name": "ruleDesc", "label": ""}]}, {"type": "tpl", "label": "", "id": "u:27b84647a337", "placeholder": "-", "name": "promoSelect", "width": 150, "inline": true, "tpl": "${promoDTOList[0].ruleDesc}", "visibleOn": "${promoDTOList.length === 1}"}], "style": {"display": "block", "position": "static"}, "isFixedHeight": false, "isFixedWidth": false}, {"type": "tpl", "id": "u:3d013017686a", "placeholder": "-", "label": "最小中包装", "name": "mpacking", "quickEdit": false, "inline": true, "tpl": "${mpacking ? INT(mpacking) : mpacking}"}, {"label": "商品编码", "id": "u:3302578dd469", "placeholder": "-", "quickEdit": false, "inline": true, "type": "text", "name": "goodsNo", "fixed": "", "showBadge": false, "copyable": true, "width": 100}, {"label": "箱装", "id": "u:08789e613088", "placeholder": "-", "quickEdit": false, "inline": true, "type": "text", "name": "caseqty"}, {"label": "国药准字", "id": "u:ba8a897a48f0", "placeholder": "-", "name": "apprdocno", "quickEdit": false, "inline": true, "type": "tpl", "popOver": {"trigger": "hover", "position": "left-top", "showIcon": false, "body": [{"type": "tpl", "tpl": "${apprdocno}", "id": "u:0f04cbc0b8c3"}], "id": "u:104d9dc732ce", "affixFooter": false}, "tpl": "${apprdocno|truncate:8}"}, {"label": "OTC标志", "id": "u:2a2b3a8c12b0", "placeholder": "-", "name": "rxtype", "quickEdit": false, "inline": true, "type": "text"}, {"label": "商品大类", "id": "u:67b598d504b9", "placeholder": "-", "name": "classone", "quickEdit": false, "inline": true, "type": "text"}, {"label": "中包装量", "id": "u:1d04bc40dbcc", "placeholder": "-", "name": "innerqty", "quickEdit": false, "inline": true}, {"label": " 最小包装量批发价", "id": "u:1d04bc423bcc", "placeholder": "-", "name": "packetPrice", "quickEdit": false, "inline": true}, {"label": "规格型号", "id": "u:1972cda46b34", "placeholder": "-", "name": "jhiSpecification", "quickEdit": false, "inline": true, "type": "text"}, {"label": "制剂规格", "id": "u:a58383086dd5", "placeholder": "-", "name": "prepspec", "quickEdit": false, "inline": true, "type": "tpl", "width": 100, "tpl": "${prepspec|truncate:8}", "popOver": {"trigger": "hover", "position": "left-top", "showIcon": false, "body": [{"type": "tpl", "tpl": "${prepspec}", "id": "u:4d04ff1fc3c7"}], "id": "u:657d9af48a8f", "affixFooter": false}}, {"label": "经营属性", "id": "u:041042383a5b", "placeholder": "-", "name": "goodsline", "quickEdit": false, "inline": true, "type": "text"}, {"label": "产地", "id": "u:968c7290406f", "placeholder": "-", "name": "habitat", "quickEdit": false, "inline": true, "type": "tpl", "width": 100, "tpl": "${habitat|truncate:6}", "popOver": {"trigger": "hover", "position": "left-top", "showIcon": false, "body": [{"type": "tpl", "tpl": "${habitat}", "id": "u:3dfc2d097994"}], "id": "u:504e5d17bb03"}}, {"label": "采购负责人", "id": "u:0410409383a5b", "placeholder": "-", "name": "purchaseUser", "quickEdit": false, "inline": true, "type": "text"}, {"label": "计量单位", "id": "u:b904739b44ba", "placeholder": "-", "quickEdit": false, "inline": true, "name": "unit"}, {"label": "上次销售日期", "id": "u:77c440b631a0", "placeholder": "-", "name": "lastSaleDate", "quickEdit": false, "inline": true, "type": "text"}, {"label": "上次销售数量", "id": "u:ab2cd79d4e56", "placeholder": "-", "name": "lastSaleCount", "type": "text", "quickEdit": false, "inline": true}, {"label": "数量", "id": "u:0fcc455ee90e", "placeholder": "", "type": "input-number", "name": "purchase_quantity", "width": 90, "fixed": "right", "keyboard": false, "step": 1, "disabledOn": "${OR(priceStatus !== 0,!availableStock, availableStock == 0)}", "precision": 0, "showSteps": false, "onEvent": {"change": {"weight": 0, "actions": [{"ignoreError": false, "script": "\nsetTimeout(() => {\n  doAction({\n    actionType: 'setValue',\n    componentId: 'u:262a314ce704',\n    args: {\n      value: {\n        goodsModiRows: context.props.store.modifiedRows\n      }\n    }\n  })\n}, 0)\n", "actionType": "custom", "args": {}}]}, "focus": {"weight": 0, "actions": [{"ignoreError": false, "script": "console.error(event, context)\ncontext.props.onRowClick(context.props.row, context.props.rowIndex);", "actionType": "custom", "args": {}}]}}, "prefix": "", "className": "purchase_quantity"}, {"type": "input-number", "label": "价格", "placeholder": "-", "width": 115, "fixed": "right", "id": "u:b83df6b38702", "name": "settleprice", "keyboard": false, "step": 1, "disabledOn": "${OR(priceStatus !== 0,!availableStock, availableStock == 0)}", "precision": 5, "showSteps": false, "onEvent": {"change": {"weight": 0, "actions": [{"ignoreError": false, "script": "setTimeout(() => {\n  doAction({\n    actionType: 'setValue',\n    componentId: 'u:262a314ce704',\n    args: {\n      value: {\n        goodsModiRows: context.props.store.modifiedRows\n      }\n    }\n  })\n}, 0)\n", "actionType": "custom", "args": {}}]}}}, {"label": "操作", "id": "u:2fedfd8b5f80", "placeholder": "-", "type": "operation", "width": 130, "fixed": "right", "size": "sm", "buttons": [{"type": "button", "label": "复制", "onEvent": {"click": {"actions": [{"ignoreError": false, "actionType": "copy", "args": {"copyFormat": "text/plain", "content": "编码:  ${goodsNo}\n名称:  ${goodsName}\n计量单位：${unit}\n厂家: ${manufacturer}\n产地: ${habitat}\n价格: ${wholesalePrice}\n效期: ${recentlyDate}\n最小中包装: ${mpacking}\n中包装量: ${innerqty}\n国药准字号: ${apprdocno}\nOTC标识: ${rxtype}\n"}}]}}, "id": "u:b684fc17b377", "level": "link", "disabledOnAction": false}, {"type": "button", "label": "批次库存", "onEvent": {"click": {"actions": [{"ignoreError": false, "actionType": "dialog", "dialog": {"type": "dialog", "title": "批次库存", "body": [{"type": "property", "title": "", "items": [{"label": "商品编码", "content": "${goodsNo}", "span": 1}, {"label": "商品名称", "content": "${goodsName}", "span": 2}, {"label": "可销库存", "content": "${availableStock}", "span": 1}], "id": "u:247abde1585b", "column": 4, "mode": "table"}, {"type": "crud", "syncLocation": false, "api": {"method": "post", "url": "/purchase-data/api/sap/stockDcList", "messages": {}, "requestAdaptor": "", "adaptor": "console.log('StockDcDetailDTO', payload)\nreturn payload", "data": {"&": "$$", "businessId": "$businessId", "goodsCode": "$goodsNo", "stockType": 3}}, "bulkActions": [], "itemActions": [], "columns": [{"name": "batchNo", "label": "生产批号", "type": "text", "id": "u:730613dbf7b9", "placeholder": "-"}, {"type": "text", "label": "生产日期", "name": "produceDate", "id": "u:ba136580a760", "placeholder": "-", "inline": true, "visible": true}, {"type": "text", "label": "有效期至/效期", "name": "expireDate", "id": "u:abe8152e923a", "placeholder": "-", "inline": true, "visible": true}, {"type": "text", "label": "数量", "name": "stock", "id": "u:96be9d264dee", "placeholder": "-", "inline": true}], "id": "u:2bc4e5daf9b3", "perPageAvailable": [5, 10, 20, 50, 100], "messages": {}, "columnsTogglable": true, "footerToolbar": [{"type": "statistics", "align": "left"}, {"type": "pagination", "align": "right"}]}], "id": "u:0e9e3a5140be", "actions": [{"type": "button", "actionType": "cancel", "label": "取消", "id": "u:a263f98d5510"}], "showCloseButton": true, "closeOnOutside": true, "closeOnEsc": true, "showErrorMsg": true, "showLoading": true, "draggable": false, "editorSetting": {"displayName": "batch_inventory"}, "actionType": "dialog", "size": "md", "hideActions": false}, "data": {"goodsName": "$goodsName", "goodsNo": "${goodsNo}", "availableStock": "${INT(availableStock)}", "businessId": "${userBusinessId}"}}]}}, "id": "u:71f540d0d827", "level": "link", "size": "lg"}, {"type": "button", "label": "缺货登记", "onEvent": {"click": {"actions": [{"ignoreError": false, "actionType": "dialog", "dialog": {"type": "dialog", "title": "商品缺货登记", "body": [{"id": "u:2a1d26bb27dd", "type": "form", "title": "表单", "mode": "horizontal", "dsType": "api", "feat": "Insert", "body": [{"type": "input-text", "label": "商品名称", "name": "goodsName", "id": "u:77d615db24ac", "static": true}, {"name": "goodsNum", "label": "缺货数量", "type": "input-number", "id": "u:2493007d7f17", "keyboard": true, "step": 1, "min": 1, "required": true}], "api": {"url": "/purchase-data/api/cmall/shortageRegistration", "method": "post", "requestAdaptor": "", "adaptor": "", "messages": {}, "data": {"businessId": "$userBusinessId", "storeId": "$channelStoreId", "goodsNo": "$goodsNo", "goodsNum": "$goodsNum", "goodsName": "$goodsName", "dcStoreId": "$dcStoreId"}}, "actions": [{"type": "button", "label": "提交", "onEvent": {"click": {"actions": [{"actionType": "submit", "componentId": "u:2a1d26bb27dd"}]}}, "level": "primary"}], "resetAfterSubmit": true, "labelAlign": "left"}], "id": "u:0a590b55cd70", "actions": [{"type": "button", "actionType": "cancel", "label": "取消", "id": "u:e2ab43d8f682"}, {"type": "button", "actionType": "confirm", "label": "确定", "primary": true, "id": "u:0ab8ac6cac2c", "disabledOnAction": false}], "showCloseButton": true, "closeOnOutside": false, "closeOnEsc": false, "showErrorMsg": true, "showLoading": true, "draggable": false, "editorSetting": {"displayName": "商品缺货登记"}, "actionType": "dialog"}}]}}, "id": "u:f0a085ec5507", "level": "link", "size": "lg", "disabledOnAction": false}], "className": "gj-kp-operation"}], "perPageAvailable": [5, 10, 20], "messages": {}, "showHeader": true, "columnsTogglable": true, "affixHeader": true, "filter": {"title": "查询条件", "body": [{"id": "search_goods_name_id", "type": "input-text", "label": "", "name": "search_goods_name", "size": "lg", "clearable": true, "placeholder": "请输入 商品名称/编码/助记码/生产厂家", "className": "search_goods_name_hotkey mb-none", "trimContents": false}, {"id": "u:fb3c4e0bd97d", "type": "input-text", "label": "生产批号", "name": "search_scph", "size": "md", "onEvent": {"focus": {"weight": 0, "actions": []}}, "clearable": true, "className": "mb-none"}, {"type": "hidden", "name": "channelStoreId", "id": "u:a6b5d1b202a8"}, {"id": "u:e073d9ab443b", "type": "submit", "label": "查询", "onEvent": {"click": {"weight": 0, "actions": [{"ignoreError": false, "script": "console.log(event)\nconsole.log(context)", "actionType": "custom"}, {"componentId": "is_head_collapsable_id", "ignoreError": false, "actionType": "setValue", "args": {"value": "${true}"}}]}}, "primary": true, "level": "default", "visible": true, "disabledOn": "${!channelStoreId}"}, {"type": "button", "label": "商品批导", "onEvent": {"click": {"actions": [{"ignoreError": false, "actionType": "url", "args": {"url": "/purchase/amisPageV2/ndDkqMwOJi?channelStoreId=$channelStoreId", "blank": false}}], "weight": 0}}, "id": "u:d17742bae6ef", "level": "default", "themeCss": {"className": {"padding-and-margin:default": {"marginLeft": "0"}}}, "disabledOn": "${!channelStoreId}", "block": false, "visible": true}], "id": "u:270bcdb1e5ca", "actions": [{"type": "submit", "label": "搜索", "primary": true, "id": "u:9b0edd0001f4"}], "feat": "Insert", "wrapWithPanel": false, "debug": false, "preventEnterSubmit": false, "className": "goods_search_area_zc", "mode": "inline"}, "showBadge": true, "itemBadge": {"text": "促销", "mode": "ribbon", "position": "top-left", "level": "error", "visibleOn": "${ablePromo}"}, "initFetch": false, "name": "goods_search_list", "syncResponse2Query": false, "footerToolbar": [{"type": "pagination", "tpl": "内容", "wrapperComponent": "", "id": "u:568cd097c663", "align": "right"}, {"type": "statistics", "tpl": "内容", "wrapperComponent": "", "id": "u:7f2439e5dcd8"}], "perPage": 10, "onEvent": {"rowClick": {"actions": [{"actionType": "custom", "ignoreError": false, "script": "window.selectRow_kpzs(event.data.index);\n"}]}, "quickSaveSucc": {"actions": [{"actionType": "custom", "args": {}, "ignoreError": false, "script": "console.error(event, context, '快速保存成功');\nevent.setData({\n  ...event.data,\n  isInsertCart: event.data.result.check_status === 0\n})\nwindow.KUAIPI_JIAGOU_isInsertCart = event.data.result.check_status === 0"}, {"ignoreError": false, "actionType": "dialog", "dialog": {"type": "dialog", "title": "${result.title}", "body": [{"type": "flex", "items": [{"type": "container", "body": [{"type": "tpl", "id": "u:abe8cc3a8058", "tpl": "${result.title}", "inline": true, "wrapperComponent": "", "hidden": false, "visible": true}], "size": "none", "style": {"position": "absolute", "display": "block", "flex": "1 1 auto", "flexGrow": 1, "inset": "auto"}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:061c30d347e4", "wrapperCustomStyle": {"z-index": "-1", "height": "0", "overflow": "hidden"}, "originPosition": "right-bottom"}, {"type": "container", "body": [{"type": "list", "id": "u:2cba5692c0bf", "source": "${UNIQ(result.store_check_info.certificateCheckList, 'desc')}", "listItem": {"body": [{"type": "wrapper", "body": [{"type": "tpl", "tpl": "${desc}", "id": "u:907cfc49bff5", "style": {"position": "static", "display": "block"}, "isFixedHeight": false, "isFixedWidth": false, "themeCss": {"baseControlClassName": {"padding-and-margin:default": {"padding": 0}}}, "wrapperComponent": ""}], "id": "u:0276e9cab8f7", "style": {"position": "static", "display": "block", "padding": 0, "fontFamily": "", "fontSize": 12}, "isFixedHeight": false, "isFixedWidth": false, "size": "xs"}], "id": "u:e373abad48ee", "actions": [], "titleClassName": "m-none p-none", "avatarClassName": "thumb-sm avatar"}, "placeholder": "", "visibleOn": "${result.store_check_info.certificateCheckList && result.store_check_info.certificateCheckList.length}"}], "size": "none", "style": {"position": "static", "display": "block", "flex": "0 0 auto"}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:b92c2aae9a1d"}, {"type": "container", "body": [{"type": "list", "id": "u:0e101f09f785", "source": "${UNIQ(result.scope_check_info.businessScopeList, 'desc')}", "listItem": {"body": [{"type": "wrapper", "body": [{"type": "tpl", "tpl": "${desc}", "id": "u:e70a38350c15", "wrapperComponent": ""}], "id": "u:efb39d60964b", "style": {"position": "static", "display": "block", "padding": 0, "fontFamily": "", "fontSize": 12}, "isFixedHeight": false, "isFixedWidth": false, "size": "xs"}], "id": "u:383f3ba32038", "actions": [], "titleClassName": "m-none p-none"}, "placeholder": "", "visibleOn": "${result.scope_check_info.businessScopeList && result.scope_check_info.businessScopeList.length}"}], "size": "none", "style": {"position": "static", "display": "block", "flex": "0 0 auto"}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:30c934af4257"}, {"type": "container", "body": [{"type": "tpl", "tpl": "${result.store_info}", "inline": true, "wrapperComponent": "", "id": "u:c0351f5d8ca9"}], "size": "none", "style": {"position": "static", "display": "block", "flex": "0 0 auto"}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:9236cbd7be61"}], "style": {"position": "relative", "rowGap": "10px", "columnGap": "0px", "flexDirection": "column", "flexWrap": "nowrap", "inset": "auto", "maxHeight": "360px", "overflowY": "scroll", "placeContent": "start"}, "id": "u:aaa6466f2ea5", "isFixedHeight": false, "isFixedWidth": false, "className": "check_goods_search_zizhi"}], "id": "u:0ba91c9980f5", "actions": [{"type": "button", "id": "u:f5b828914154", "label": "复制并取消", "onEvent": {"click": {"weight": 0, "actions": [{"ignoreError": false, "actionType": "custom", "args": {}, "script": "const content = document.querySelector('.check_goods_search_zizhi');\ndoAction({\n  \"ignoreError\": false,\n  \"actionType\": \"copy\",\n  \"args\": {\n    \"content\": `${content.innerText}`\n  }\n})\n\nconsole.error('复制');\n\nwindow.KUAIPI_JIAGOU_isInsertCart = false;"}, {"ignoreError": false, "actionType": "closeDialog"}]}}}, {"type": "button", "label": "仍然加购(Enter)", "primary": true, "id": "u:939b2d67defb", "onEvent": {"click": {"weight": 0, "actions": [{"ignoreError": false, "script": "window.KUAIPI_JIAGOU_isInsertCart = true;", "actionType": "custom", "args": {}}]}}, "actionType": "confirm", "hotKey": "enter"}], "showCloseButton": true, "closeOnOutside": false, "closeOnEsc": false, "showErrorMsg": true, "showLoading": true, "draggable": false, "editorSetting": {"displayName": "资质校验信息"}, "actionType": "dialog", "size": "custom", "style": {"width": "800px", "max-height": "500px"}}, "waitForAction": true, "outputVar": "checkResult", "expression": "${!isInsertCart}"}, {"ignoreError": false, "actionType": "custom", "args": {}, "script": "console.error(event, context, '秒后');\nconsole.error(window.KUAIPI_JIAGOU_isInsertCart);\nevent.setData({\n  ...event.data,\n  isInsertCart: window.KUAIPI_JIAGOU_isInsertCart\n})"}, {"ignoreError": false, "actionType": "dialog", "dialog": {"type": "dialog", "title": "", "body": [{"type": "flex", "id": "u:a9b0e7df4603", "items": [{"type": "container", "body": [{"type": "service", "body": [], "id": "u:98521c08d6ca", "dsType": "api", "showErrorMsg": false, "className": "m-t", "api": {"method": "post", "url": "/purchase-data/api/cmall/cart", "messages": {}, "requestAdaptor": "let goodsModiRows = api.data.goodsModiRows\nconsole.log('goods search add cart', goodsModiRows)\ncontext = api.context\nconsole.log('goods search add cart api', api)\n\nlet cartSaveParamList = goodsModiRows.map(item => ({\n  channelPrice: item.data.settleprice,\n  channelStoreId: context.channelStoreId,\n  count: item.data.purchase_quantity,\n  goodsNo: item.data.goodsNo,\n  ablePromo: item.data.ablePromo,\n  itemId: item.data.itemId,\n  wholesalePrice: item.data.wholesalePrice,\n  goodsMold: item.data.goodsMold,\n}))\n\nconsole.error('最终需要加购的数据', cartSaveParamList);\n\napi.data = {\n  cartSaveParamList: cartSaveParamList,\n  handleType: 1,\n  orderId: context.orderId,\n  businessId: context.userBusinessId\n}\nreturn api\n", "adaptor": "console.error(payload);\nif (payload === 'OK') {\n  return {\n    status: 0,\n    msg: '加购成功！'\n  }\n}\nreturn payload", "headers": {"channelStoreId": "$channelStoreId", "bizCode": "PARTICIPATE_VOLUME_BUSINESS", "bizFunction": "addCar"}, "data": {"goodsModiRows": "$goodsModiRows"}}, "onEvent": {"fetchInited": {"weight": 0, "actions": [{"actionType": "setValue", "componentId": "u:262a314ce704", "args": {"value": {"goodsModiRows": []}}}, {"ignoreError": false, "actionType": "custom", "script": "doAction({\n  \"componentId\": \"search_cart_goods_box_id\",\n  \"actionType\": \"clear\"\n})\nsetTimeout(function () {\n  doAction({\n    \"ignoreError\": false,\n    \"actionType\": \"reload\",\n    \"componentId\": \"shop_cart_id\",\n    \"expression\": \"\"\n  })\n}, 100);\n", "args": {}}, {"componentId": "u:270bcdb1e5ca", "ignoreError": false, "actionType": "reset", "args": {}}, {"ignoreError": false, "script": "doAction({\n  \"componentId\": \"search_goods_name_id\",\n  \"ignoreError\": false,\n  \"actionType\": \"focus\"\n}\n);\n", "actionType": "custom", "args": {}, "componentId": "search_goods_name_id"}, {"ignoreError": false, "actionType": "closeDialog"}]}}}], "size": "none", "style": {"position": "static", "display": "block", "flex": "1 1 auto", "flexGrow": 1}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:a594963f2331", "visible": true}], "style": {"position": "relative", "rowGap": "0px", "columnGap": "0px", "flexWrap": "nowrap", "inset": "auto", "flexDirection": "column"}, "isFixedHeight": false, "isFixedWidth": false}], "id": "u:2ee57de35676", "actions": [{"type": "tpl", "id": "u:ce57ca062765", "tpl": "加购中···", "inline": true, "wrapperComponent": "", "wrapperCustomStyle": {"width": "100%", "text-align": "center", "margin-top": "20px"}}], "showCloseButton": false, "closeOnOutside": false, "closeOnEsc": false, "showErrorMsg": true, "showLoading": true, "draggable": false, "editorSetting": {"displayName": "加购全局loading弹窗"}, "actionType": "dialog", "size": "sm"}, "expression": "${isInsertCart}", "stopPropagation": ""}, {"actionType": "preventDefault"}]}}, "headerToolbar": [{"type": "columns-toggler", "align": "right", "draggable": true, "overlay": true, "footerBtnSize": "sm"}, {"type": "button", "id": "u:3bf35f193ec5", "label": "B2B商城", "onEvent": {"click": {"weight": 0, "actions": [{"ignoreError": false, "actionType": "url", "args": {"url": "/purchase/indexPage?channelStoreId=$channelStoreId"}}]}}, "level": "primary", "themeCss": {"className": {"padding-and-margin:default": {"marginLeft": "-0.8rem"}}}, "disabledOnAction": false}, {"type": "button", "id": "u:425d2eba9882", "label": "SAP价格查询", "onEvent": {"click": {"weight": 0, "actions": [{"ignoreError": false, "actionType": "url", "args": {"url": "https://sapet0app01.gaojihealth.com:44300/sap/bc/gui/sap/its/webgui/?~transaction=ZSDR0002"}}]}}, "level": "default", "themeCss": {"className": {"padding-and-margin:default": {}}}}, {"type": "button", "id": "u:85017130b0b1", "label": "加购 (Insert/F9)", "onEvent": {"click": {"weight": 0, "actions": [{"ignoreError": false, "script": "\n\nconsole.log(event, context, context.props.store.modifiedRows);\n\nconst modifiedRows = event.data.goodsModiRows || [];\n\nconst cartList = event.data.cartListDtoList || [];\n\n/// 校验最小包装和校验数据量不能超过1万\nconst checkInvalidCount = (record) => {\n  let { availableStock, purchase_quantity, mpacking, innerqty, settleprice, goodsMold, maxPrice, minPrice, costPrice, wholesalePrice } = record;\n\n  // 价格：settleprice\n  // 最低限价： minPrice\n  // 最高限价：maxPrice\n  // 成本价：costPrice\n  // 批发价：wholesalePrice （目录价）\n  let curInputPrice = Number(settleprice)\n  minPrice = Number(minPrice);\n  maxPrice = Number(maxPrice);\n  costPrice = Number(costPrice);\n  wholesalePrice = Number(wholesalePrice);\n  const wcMinPrice = Math.min(costPrice, wholesalePrice);\n  const wmMinPrice = Math.min(minPrice, wholesalePrice);\n\n  const limitMount = Number(mpacking ? mpacking : innerqty);\n  let invalid = false\n  let errorMsg = ''\n  if (purchase_quantity < 1 || purchase_quantity > availableStock) {\n    invalid = true;\n    errorMsg = '数量必须在1和库存数量之间'\n  } else if (limitMount && purchase_quantity % Math.floor(limitMount) != 0) {\n    invalid = true;\n    errorMsg = '数量必须是最小包装量或者中包装量的整数倍'\n  }\n  // 锁价品\n  if (goodsMold == 2 && curInputPrice < wholesalePrice) {\n    // 有最低限价和最高限价，且最低限价 = 最高限价 = 批发价,填写价格不能比批发价低\n    invalid = true;\n    errorMsg = '锁价品填写值(' + curInputPrice + ')不能比批发价(' + wholesalePrice + ')低'\n  } else if (!minPrice && costPrice && wholesalePrice && curInputPrice < wcMinPrice) {// 仅有批发价和成本价，不能低于两者的最小值\n    const wcDesc = costPrice === wcMinPrice ? '成本价' : '批发价'\n    invalid = true;\n    errorMsg = `填写值(${curInputPrice})不能比${wcDesc}(${wcMinPrice})低`\n  } else if (wholesalePrice && minPrice && curInputPrice < wmMinPrice) { // 包含批发价和最低限价，不能低于两者的最小值\n    const wmDesc = minPrice === wmMinPrice ? '最低限价' : '批发价'\n    invalid = true;\n    errorMsg = `填写值(${curInputPrice})不能比${wmDesc}(${wmMinPrice})低`\n  }\n  let result = { invalid: invalid, errorMsg: errorMsg };\n  console.log('check result', result)\n  return result\n};\n/// 校验购物车是否已存在\nconst checkHasInCart = (record) =>\n  cartList.some((item) => item.itemId === record.itemId);\n\nconst invalidRows = modifiedRows.map(record => {\n  const { invalid, errorMsg } = checkInvalidCount(record.data)\n  return {\n    ...record.data,\n    invalid: invalid,\n    errorMsg: errorMsg,\n    availableStock: Math.floor(record.data.availableStock),\n    indexNo: record.index + 1\n  }\n}).filter((record) => {\n  return record.invalid\n});\nconsole.log('invalidRows:::::', invalidRows);\nif (invalidRows.length > 0) {\n  doAction({\n    actionType: 'dialog',\n    dialog: {\n      title: false,\n      data: {\n        rows: invalidRows,\n      },\n      size: \"lg\",\n      body: [\n        {\n          type: 'table',\n          title: false,\n          columnsTogglable: false,\n          source: '$rows',\n          columns: [\n            {\n              name: 'indexNo',\n              label: '序号',\n              fixed: 'left',\n            },\n            {\n              name: 'goodsName',\n              label: '商品',\n              fixed: 'left',\n            },\n            {\n              name: 'availableStock',\n              label: '库存',\n              fixed: 'left',\n            },\n            /* {\n              name: 'manufacturer',\n              label: '厂家',\n              fixed: 'left'\n            },\n            {\n              name: 'jhiSpecification',\n              label: '规格型号',\n            }, */\n            {\n              name: 'mpacking',\n              label: '最小中包装',\n            },\n            {\n              name: 'innerqty',\n              label: '中包装量',\n            },\n            {\n              name: 'purchase_quantity',\n              label: '数量',\n            },\n            {\n              name: 'errorMsg',\n              label: '错误',\n            },\n          ],\n        },\n      ],\n      actions: [\n        {\n          type: 'button',\n          actionType: 'confirm',\n          hotKey: 'enter',\n          label: '确定[Enter]',\n          primary: true,\n        },\n      ],\n    },\n  });\n  return;\n}\n\nconst repeatRows = modifiedRows.filter((record) => checkHasInCart(record.data));\nconst noRepeatRows = modifiedRows.filter(\n  (record) => !checkHasInCart(record.data)\n);\n\nconsole.error('repeatRows:::::', repeatRows, noRepeatRows);\n\nif (repeatRows.length > 0) {\n  const source = repeatRows.map((item) => ({\n    ...item.data,\n    indexNo: item.index + 1,\n  }));\n  doAction({\n    actionType: 'dialog',\n    dialog: {\n      title: '提示',\n      data: {\n        rows: source,\n        noRepeatRows: noRepeatRows\n      },\n      body: [\n        {\n          type: 'tpl',\n          tpl: '<div>以下商品已在购物车，<span style=\"color:red\">【确认】继续加购？【取消】仅加购新商品。</span></div>',\n        },\n        {\n          type: 'table',\n          title: false,\n          columnsTogglable: false,\n          source: '$rows',\n          columns: [\n            {\n              name: 'indexNo',\n              label: '序号',\n              fixed: 'left',\n            },\n            {\n              name: 'goodsName',\n              label: '商品',\n              fixed: 'left',\n            },\n            /* {\n              name: 'manufacturer',\n              label: '厂家',\n              fixed: 'left'\n            },\n            {\n              name: 'jhiSpecification',\n              label: '规格型号',\n            }, */\n            {\n              name: 'mpacking',\n              label: '最小中包装',\n            },\n            {\n              name: 'innerqty',\n              label: '中包装量',\n            },\n            {\n              name: 'purchase_quantity',\n              label: '数量',\n            },\n          ],\n        },\n      ],\n      actions: [\n        {\n          type: 'button',\n          actionType: 'cancel',\n          hotKey: 'delete,backspace',\n          label: '取消[Del]',\n          onEvent: {\n            click: {\n              weight: 0,\n              actions: [\n                {\n                  actionType: 'custom',\n                  script:\n                    \"console.log(event, context)\\ndoAction({\\n    actionType: 'setValue',\\n    componentId: 'u:262a314ce704',\\n    args: {\\n      value: {\\n        goodsModiRows: event.data.noRepeatRows\\n      }\\n    }\\n  })\\n\",\n                },\n                {\n                  actionType: 'custom',\n                  script:\n                    \"setTimeout(() => {\\n  console.error(event.data);\\n  if (event.data.noRepeatRows.length <= 0) return;\\n  doAction({\\n    actionType: 'submitQuickEdit',\\n    componentId: 'goods_search_crud_zc'\\n  })\\n}, 0)\",\n                },\n              ],\n            },\n          },\n        },\n        {\n          type: 'button',\n          actionType: 'confirm',\n          hotKey: 'enter',\n          label: '确定[Enter]',\n          primary: true,\n          onEvent: {\n            click: {\n              weight: 0,\n              actions: [\n                {\n                  actionType: 'custom',\n                  script:\n                    \"console.log(event, context);\\ndoAction({\\n  actionType: 'submitQuickEdit',\\n  componentId: 'goods_search_crud_zc'\\n})\",\n                },\n              ],\n            },\n          },\n        },\n      ],\n    },\n  });\n  return;\n}\n\ndoAction({\n  actionType: 'submitQuickEdit',\n  componentId: 'goods_search_crud_zc'\n})\n\n", "actionType": "custom", "args": {}}], "debounce": {"wait": 300}}}, "className": "goods_jiagou_hotkey_zc", "disabledOn": "${!goodsModiRows || !goodsModiRows.length}"}, {"type": "tpl", "id": "u:05e1812c702b", "tpl": "<p class=\"mb-none  text-danger\">${goodsModiRows.length ? '(当前有 ' + ${goodsModiRows.length} +' 条记录修改但没有提交，你可以执行加购操作)' : ''}</p>", "wrapperComponent": ""}, {"type": "button", "id": "u:343e8e98a988", "label": "加购数据清空", "onEvent": {"click": {"weight": 0, "actions": [{"ignoreError": false, "script": "doAction({\n  \"actionType\": \"setValue\",\n  \"componentId\": \"u:262a314ce704\",\n  \"args\": {\n    \"value\": {\n      \"goodsModiRows\": []\n    }\n  }\n})", "actionType": "custom", "args": {}}]}}, "className": "goods_jiagou_hotkey_clear_zc invisible"}, {"type": "tpl", "tpl": "快捷键分页：上一页【Ctrl+←】\\下一页【Ctrl+→】", "wrapperComponent": "", "id": "u:ad7004785cbc", "align": "right"}, {"type": "statistics", "tpl": "内容", "wrapperComponent": "", "id": "u:32baf8e55da3", "align": "right"}], "footable": false, "itemActions": [], "defaultParams": {}, "quickSaveApi": {"method": "post", "url": "/gsp/api/user/purchaseRestriction/checkRestriction", "messages": {"success": ""}, "requestAdaptor": "\napi.data.bizCode = \"PARTICIPATE_VOLUME_BUSINESS\";\napi.data.itemInfos = api.data.goodsModiRows.map((item) => {\n  return {\n    itemNo: item.data.goodsNo\n  }\n})\ndelete api.data.goodsModiRows\nconsole.error(api)\nreturn api", "adaptor": "if (payload.status === -1) {\n    return {\n        status: 0,\n        data: {\n            check_status: 0,\n        }\n    }\n}\nreturn payload;", "data": {"goodsModiRows": "$goodsModiRows", "storeId": "$channelStoreId"}}, "keepItemSelectionOnPageChange": true, "primaryField": "itemId", "header": [], "hideQuickSaveBtn": true, "placeholder": "暂无商品", "rowClassNameExpr": "${index % 2 ? '' : 'bg-gray-100'} ${YEARS(recentlyDate, TODAY())==0 ? 'text-gjred' : ''}", "bordered": true, "className": "gj-Table-bordered", "$$comments": {"$columns": {"3": [["\n    // {\n    ", "//   \"name\": \"${goodsName}\",\n    ", "//   \"type\": \"container\",\n    ", "//   \"id\": \"u:b2880314f1ba\",\n    ", "//   \"label\": \"商品\",\n    ", "//   \"placeholder\": \"-\",\n    ", "//   \"copyable\": true,\n    ", "//   \"popOver\": {\n    ", "//     \"type\": \"panel\",\n    ", "//     \"title\": \"${goodsName}\",\n    ", "//     \"body\": [\n    ", "//       {\n    ", "//         \"type\": \"image\",\n    ", "//         \"id\": \"u:d4f5a9125719\",\n    ", "//         \"enlargeAble\": true,\n    ", "//         \"maxScale\": 200,\n    ", "//         \"minScale\": 50,\n    ", "//         \"position\": \"right-top\",\n    ", "//         \"name\": \"picUrl\",\n    ", "//         \"style\": {\n    ", "//           \"display\": \"inline-block\"\n    ", "//         }\n    ", "//       }\n    ", "//     ],\n    ", "//     \"id\": \"u:b7ead3824aa1\",\n    ", "//     \"affixFooter\": false\n    ", "//   },\n    ", "//   \"body\": [\n    ", "//     {\n    ", "//       \"type\": \"text\",\n    ", "//       \"name\": \"${goodsName}\",\n    ", "//       \"id\": \"u:703eca43c55e\",\n    ", "//       \"label\": \"商品\",\n    ", "//       \"placeholder\": \"-\",\n    ", "//       \"inline\": true,\n    ", "//       \"quickEdit\": false,\n    ", "//       \"width\": 60,\n    ", "//       \"copyable\": true,\n    ", "//       \"fixed\": \"left\",\n    ", "//       \"popOver\": {\n    ", "//         \"type\": \"panel\",\n    ", "//         \"title\": \"${goodsName}\",\n    ", "//         \"body\": [\n    ", "//           {\n    ", "//             \"type\": \"image\",\n    ", "//             \"id\": \"u:d4f5a9125719\",\n    ", "//             \"enlargeAble\": true,\n    ", "//             \"maxScale\": 200,\n    ", "//             \"minScale\": 50,\n    ", "//             \"position\": \"right-top\",\n    ", "//             \"name\": \"picUrl\",\n    ", "//             \"style\": {\n    ", "//               \"display\": \"inline-block\"\n    ", "//             }\n    ", "//           }\n    ", "//         ],\n    ", "//         \"id\": \"u:b7ead3824aa1\",\n    ", "//         \"affixFooter\": false\n    ", "//       }\n    ", "//     },\n    ", "//     {\n    ", "//       \"type\": \"tpl\",\n    ", "//       \"name\": \"${goodsName}\",\n    ", "//       \"id\": \"u:79cd7f16680d\",\n    ", "//       \"tpl\": \"<div>追溯码</div>\",\n    ", "//       \"wrapperCustomStyle\": {\n    ", "//         \"padding\": \"2px 0px\",\n    ", "//         \"color\": \"white\",\n    ", "//         \"border-radius\": \"8px\",\n    ", "//         \"background-color\": \"red\",\n    ", "//         \"display\": \"block\",\n    ", "//         \"text-align\": \"center\",\n    ", "//         \"width\": \"60px\"\n    ", "//       },\n    ", "//       \"visibleOn\": \"${regulatoryCode == '是'}\"\n    ", "//     }\n    ", "//   ],\n    ", "//   \"style\": {\n    ", "//     \"position\": \"relative\",\n    ", "//     \"display\": \"flex\",\n    ", "//     \"inset\": \"auto\",\n    ", "//     \"flexWrap\": \"nowrap\",\n    ", "//     \"flexDirection\": \"column\",\n    ", "//     \"alignItems\": \"flex-start\"\n    ", "//   },\n    ", "//   \"size\": \"none\",\n    ", "//   \"wrapperBody\": false\n    ", "// },\n    "]]}}}], "title": "商品搜索区 <span style='color:red'>(当前购物车初始加购<span style='color:red;font-size:20px;'>${(initTotalGoodsCount == null ||initTotalGoodsCount == undefined)   ? '*' : initTotalGoodsCount}</span>件（不包括赠品），现总加购<span style='color:red;font-size:20px;'>${(totalPriceArea == null || totalPriceArea == undefined) ? '*' : totalPriceArea.totalGoodsCount}</span>件商品)</span>", "bodyClassName": "m-b", "className": "gooods_search_hotkey", "collapsable": false, "subFormMode": "", "headingClassName": "", "visibleOn": "${!isHideGoodsTable}"}, {"type": "flex", "id": "u:f2aa7b75602e", "items": [{"type": "container", "body": [], "size": "none", "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:bb768650e683", "style": {"position": "static", "display": "block", "flex": "1 1 auto", "flexGrow": 1, "flexBasis": "0px"}}], "style": {"position": "relative", "rowGap": "10px", "columnGap": "10px", "flexWrap": "nowrap", "height": "20px", "overflowY": "visible", "inset": "auto"}, "isFixedHeight": true, "isFixedWidth": false, "themeCss": {"baseControlClassName": {"background:default": "#eff0f4", "padding-and-margin:default": {"margin": "0 -0.75rem 1.25rem"}}}}, {"type": "fieldSet", "id": "u:a53a246b73b4", "style": {"flexWrap": "nowrap", "position": "static"}, "isFixedHeight": false, "isFixedWidth": false, "body": [{"type": "flex", "id": "u:cf30a3e4d25a", "items": [{"type": "container", "body": [{"type": "service", "id": "shop_cart_id", "onEvent": {"fetchInited": {"weight": 0, "actions": [{"ignoreError": false, "actionType": "custom", "args": {}, "script": "console.log('cart fetchInited event', event)\nconsole.log('cart fetchInited context', context)\n\n// 清空购物车编辑状态\nwindow.KUAIPI_CART_ISEDITING_SUBMIT = false;\nwindow.KUAIPI_CART_ISEDITING_ZANCUN_SUBMIT = false;\nwindow.KUAIPI_CART_ISEDITING = false;\nconsole.error('刷新购物车，重置编辑状态', window.KUAIPI_CART_ISEDITING);\n// 购物车400状态，自动重连2次\nif (window.KUAIPI_CART_RETRY_TIMES < 2 && event.data.responseData && event.data.responseData.KP_ERROR_CODE === 400) {\n  window.KUAIPI_CART_RETRY_TIMES += 1;\n  doAction(\n    {\n      \"componentId\": \"shop_cart_id\",\n      \"ignoreError\": false,\n      \"actionType\": \"reload\"\n    })\n  return;\n}\nwindow.KUAIPI_CART_RETRY_TIMES = 0;\n\nif (event.data.responseData) {\n  event.setData({\n    ...event.data,\n    stockLess: event.data.responseData.stockLess,\n  })\n}\n\n\ndoAction({\n  \"componentId\": \"u:262a314ce704\",\n  \"ignoreError\": false,\n  \"actionType\": \"setValue\",\n  \"args\": {\n    \"value\": {\n      \"cartListDtoList\": \"${event.data.responseData.cartListDtoList}\",\n      \"cartOrderId\": \"${event.data.responseData.cartOrderId}\",\n      \"totalPriceArea\": {\n        \"payAmount\": \"${event.data.responseData.payAmount}\",\n        \"costAmount\": \"${event.data.responseData.costAmount}\",\n        \"grossAmount\": \"${event.data.responseData.grossAmount}\",\n        \"grossRate\": \"${event.data.responseData.grossRate}\",\n        \"totalGoodsCount\": \"${event.data.responseData.cartListDtoList ? event.data.responseData.cartListDtoList.length : 0}\",\n        \"abkAmount\": \"${event.data.responseData.abkAmount}\"\n      },\n      \"topExtendParamMap\": \"${event.data.responseData.extendParamMap}\",\n      \"canFilterFromCartList\": false\n    }\n  }\n});\n\n\n\n"}, {"ignoreError": false, "actionType": "dialog", "dialog": {"type": "dialog", "title": "提示", "body": [{"type": "alert", "id": "u:2d2c5de14ba2", "title": "", "body": [{"type": "tpl", "tpl": "购物车赠品库存不足", "wrapperComponent": "", "inline": false, "id": "u:742b55b7f30a"}], "level": "warning", "className": "mb-3"}, {"type": "crud", "syncLocation": false, "api": {"method": "get", "url": ""}, "bulkActions": [], "itemActions": [], "columns": [{"name": "goodsNo", "label": "商品编码", "type": "text", "id": "u:322398afaa39", "placeholder": "-"}, {"name": "goodsName", "label": "商品名称", "type": "text", "id": "u:a0da05e62795", "placeholder": "-", "inline": true}, {"type": "tpl", "tpl": "", "inline": true, "wrapperComponent": "", "label": "购买数量", "id": "u:a2081cc52a1e", "placeholder": "-", "name": "promotionCount"}, {"type": "tpl", "tpl": "", "inline": true, "wrapperComponent": "", "label": "库存", "id": "u:0e8dfd24209f", "placeholder": "-", "name": "count"}], "id": "u:582873b3b253", "perPageAvailable": [5, 10, 20, 50, 100], "messages": {}, "source": "${event.data.responseData.changeGiftList}"}], "id": "u:0388c1512b6e", "actions": [{"type": "button", "label": "确定", "id": "u:07f666a362c4", "primary": true, "onEvent": {"click": {"weight": 0, "actions": [{"ignoreError": false, "outputVar": "responseResult", "actionType": "ajax", "options": {}, "api": {"url": "/purchase-data/api/cmall/cart?channelStoreId=$channelStoreId", "method": "post", "requestAdaptor": "", "adaptor": "", "messages": {}, "headers": {"bizCode": "PARTICIPATE_VOLUME_BUSINESS", "bizFunction": "ilstCart"}, "data": {"businessId": "$userBusinessId", "handleType": 9, "cartListParam": {"bizCode": "PARTICIPATE_VOLUME_BUSINESS", "businessId": "$userBusinessId", "channelStoreId": "$channelStoreId", "orderNo": "${orderStatusCur == -2 ? orderId : null}", "orderId": "${orderId}", "orderStatusCur": "${orderStatusCur}"}}}}, {"actionType": "stopPropagation", "expression": "${!event.data.responseData}"}, {"actionType": "reload", "componentId": "shop_cart_id", "groupType": "component"}, {"actionType": "closeDialog", "ignoreError": false}]}}}], "showCloseButton": false, "closeOnOutside": false, "closeOnEsc": false, "showErrorMsg": true, "showLoading": true, "draggable": false, "editorSetting": {"displayName": "赠品库存不足处理"}, "actionType": "dialog", "size": "md"}, "expression": "${stockLess === 1}"}]}}, "api": {"method": "post", "url": "/purchase-data/api/cmall/cart?channelStoreId=$channelStoreId", "requestAdaptor": "console.log('===api，购物车列表', api)\nif (api.context.canFilterFromCartList) {\n  let filtDtoList = api.context.cartListDtoList\n  if (api.context.cartSearchKeyword && api.context.cartListDtoList && \n    api.context.cartListDtoList.length > 0) {\n    filtDtoList = api.context.cartListDtoList.filter(item => item.goodsNo.includes(api.context.cartSearchKeyword) || item.goodsName.includes(api.context.cartSearchKeyword) )\n  }\n  console.log('mock api2', api.context.cartSearchKeyword)\n  console.log('mock api3', filtDtoList)\n  let data = {\n    url: api.url,\n    // ...api,\n    // filterCartListDtoList: filtDtoList,\n    mockResponse: {\n      status: 200,\n      data: {\n        status: 0,\n        data: {\n          cartListDtoList: api.context.cartListDtoList,\n          filterCartListDtoList: filtDtoList,\n          extendParamMap: api.context.topExtendParamMap,\n          ...api.context.totalPriceArea\n        },\n        msg: ''\n      },\n      msg: ''\n    }\n  };\n  console.log('mock api4', data)\n  return data\n} else {\n  let data = {\n    handleType: 4,\n    businessId: api.context.userBusinessId,\n    cartListParam: {\n      bizCode: \"PARTICIPATE_VOLUME_BUSINESS\",\n      businessId: api.context.userBusinessId,\n      channelStoreId: api.context.channelStoreId,\n      orderNo: api.context.orderStatusCur == -2 ? api.context.orderId : null,\n      orderId: api.context.orderId,\n      orderStatusCur: api.context.orderStatusCur\n    }\n  }\n  console.log('===data', data)\n  api.data = data\n  return api;\n}\n", "adaptor": "console.log('===shop_cart_payload 1', payload)\n\nif (payload.data.cartListDtoList == null) {\n  payload.data.cartListDtoList = []\n  payload.data.filterCartListDtoList = []\n}\nif (payload.data && payload.data.cartListDtoList && payload.data.cartListDtoList.length > 0) {\n  payload.data.filterCartListDtoList = payload.data.cartListDtoList.map(item => item)\n}\n// 购物车400状态, 自动重连2次\nif (window.KUAIPI_CART_RETRY_TIMES < 2 && payload.status === 400) {\n  payload.status = 0;\n  payload.msg = '';\n  console.log('购物车状态400异常，自动重连' + (window.KUAIPI_CART_RETRY_TIMES + 1) + '次')\n  payload.data = {\n    ...payload.data,\n    KP_ERROR_CODE: 400,\n  }\n}\n\nif (payload.data) {\n  payload.data.cartOrderId = payload.data.orderId;\n  delete payload.data.orderId;\n}\n\nconsole.log('shop_cart_payload 2', payload)\nreturn payload", "responseData": {"&": "$$"}, "sendOn": "this.channelStoreId", "data": {"&": "$$", "canFilterFromCartList": "$canFilterFromCartList", "orderId": "$orderId", "orderStatusCur": "$orderStatusCur", "cartSearchKeyword": "$cartSearchKeyword"}, "messages": {}, "headers": {"bizCode": "PARTICIPATE_VOLUME_BUSINESS", "bizFunction": "ilstCart"}}, "body": [{"type": "input-table", "columns": [{"label": "主推", "name": "wholesalepushlevel", "quickEdit": false, "id": "u:16397498214c", "placeholder": "-", "type": "text", "inline": true, "fixed": "left"}, {"label": "商品", "name": "goodsName", "quickEdit": false, "id": "u:62af0f87a512", "placeholder": "-", "inline": true, "type": "text", "fixed": "left", "className": "w", "copyable": true, "width": ""}, {"type": "text", "quickEdit": false, "name": "manufacturer", "id": "u:6eb6fdcf3ac2", "label": "厂家", "placeholder": "-", "inline": true, "fixed": "left", "width": 140, "copyable": true}, {"type": "container", "name": "regulatoryCode", "label": "追溯码", "placeholder": "-", "id": "u:3168c808b8c8", "quickEdit": false, "width": 100, "body": [{"type": "tpl", "id": "u:079d707c231d", "quickEdit": false, "style": {"text-align": "center"}, "tpl": "追溯码", "visibleOn": "${regulatoryCode == '是'}", "wrapperCustomStyle": {"background": "#E60036", "padding": "2px 5px", "color": "white", "display": "inline-block", "border-radius": "4px", "margin-left": "10px", "font-size": "12px"}}], "size": "none", "wrapperBody": false, "className": "kp-godds-first-td"}, {"label": "购物车ID", "name": "cartId", "quickEdit": false, "id": "u:3d849120ea52", "placeholder": "-", "type": "static", "inline": true, "popOver": false, "copyable": false, "visible": false}, {"label": "类型", "quickEdit": false, "id": "u:07f28fe7514f", "placeholder": "-", "type": "mapping", "style": {"border": "0.5px solid #e8e9eb"}, "name": "goodsLabelList", "map": {"1": "<span class='label label-info'>普品</span>", "2": "<span class='label label-success'>赠品</span>", "3": "<span class='label label-danger'>锁价品</span>"}}, {"label": "库存", "placeholder": "-", "inline": true, "id": "u:bf2af52c4bf5", "quickEdit": false, "type": "tpl", "style": {}, "tpl": "${INT(availableStock)}", "themeCss": {"baseControlClassName": {"border:default": {"border": "0.5px solid #e8e9eb"}}}}, {"label": "有效期", "type": "text", "id": "u:90c7e118e8e7", "placeholder": "-", "name": "recentlyDate", "inline": true, "quickEdit": false, "width": 100, "style": {"border": "0.5px solid #e8e9eb"}}, {"label": "单品促销", "id": "u:5e270b849658", "placeholder": "选择促销", "type": "select", "name": "selectPromotionIdList", "staticOn": "${!ablePromo}", "searchable": false, "source": "$promotionList", "multiple": false, "valueField": "promotionId", "labelField": "promotionTitle", "removable": false, "clearable": true, "joinValues": false, "onEvent": {"change": {"weight": 0, "actions": [{"ignoreError": false, "outputVar": "responseResult", "actionType": "ajax", "options": {}, "api": {"url": "/purchase-data/api/cmall/cart", "method": "post", "requestAdaptor": "console.error(api)\nconst cartSwitchDetailParams = api.context.promotionList.map((item) => {\n  item.select = 0\n  if (api.context.selectPromotionIdList && api.context.selectPromotionIdList.promotionId === item.promotionId) {\n    item.select = 1\n  }\n  return {\n    ...item,\n  }\n})\napi.data = {\n  cartPromotionChangeParam: {\n    bizCode: 'PARTICIPATE_B2B',\n    businessId: api.context.userBusinessId,\n    channelStoreId: api.context.channelStoreId,\n    cartSwitchSingleParams: [\n      {\n        \"cartSwitchDetailParams\": cartSwitchDetailParams,\n        \"id\": api.context.cartId\n      }\n    ]\n  },\n  orderId: api.context.orderId,\n  businessId: api.context.userBusinessId,\n  handleType: 11,\n}\nreturn api", "adaptor": "", "messages": {}}}, {"componentId": "shop_cart_id", "groupType": "component", "actionType": "reload"}]}}, "checkAll": false, "showInvalidMatch": false, "description": "", "popOverContainerSelector": "body", "menuTpl": "<div style='line-height: normal;padding: 5px 0;white-space: pre-wrap;font-size:12px;'>${promotionTypeDesc} | ${promotionTitle}</div>", "overlay": {"width": "24rem"}, "columns": [{"name": "promotionTypeDesc", "label": ""}, {"name": "promotionTitle", "label": ""}], "className": "w-36 singlepcx_kp", "inputClassName": "white-space-normal", "optionClassName": "h-auto", "popoverClassName": "singlepcx_kp_overlay"}, {"type": "tpl", "tpl": "${multiPromotionList|pick:promotionTitle|join}", "inline": true, "label": "多品促销", "id": "u:f9f0eaaf3e29", "placeholder": "-", "quickEdit": false, "wrapperComponent": "", "width": 200}, {"name": "mpacking", "label": "最小中包装", "type": "tpl", "id": "u:75953d0dbf94", "placeholder": "-", "quickEdit": false, "inline": true, "style": {}, "tpl": "${mpacking ? INT(mpacking) : mpacking}", "themeCss": {"baseControlClassName": {"border:default": {"border": "0.5px solid #e8e9eb"}}}}, {"name": "goodsNo", "label": "商品编码", "type": "text", "id": "u:f4f4c0637e60", "placeholder": "-", "quickEdit": false, "inline": true, "style": {"border": "0.5px solid #e8e9eb"}, "fixed": "", "showBadge": false, "copyable": true}, {"name": "caseqty", "label": "箱装", "id": "u:2332d404cbe3", "placeholder": "-", "quickEdit": false, "inline": true, "style": {"border": "0.5px solid #e8e9eb"}, "type": "text"}, {"label": "国药准字", "placeholder": "-", "quickEdit": false, "inline": true, "id": "u:7ad27bf16b10", "name": "apprdocno", "style": {}, "type": "tpl", "popOver": {"trigger": "hover", "position": "left-top", "showIcon": false, "body": [{"type": "tpl", "tpl": "${apprdocno}", "id": "u:0f04cbc0b8c3"}], "id": "u:104d9dc732ce", "affixFooter": false}, "tpl": "${apprdocno|truncate:8}", "themeCss": {"baseControlClassName": {"border:default": {"border": "0.5px solid #e8e9eb"}}}}, {"name": "rxtype", "label": "OTC标志", "placeholder": "-", "quickEdit": false, "inline": true, "id": "u:88a24b5d7cac", "style": {"border": "0.5px solid #e8e9eb"}, "type": "text"}, {"name": "classone", "label": "商品大类", "placeholder": "-", "quickEdit": false, "inline": true, "id": "u:8ad45dcac989", "style": {"border": "0.5px solid #e8e9eb"}, "type": "text"}, {"label": "中包装量", "name": "innerqty", "placeholder": "-", "quickEdit": false, "inline": true, "id": "u:f4ad4930eef9", "style": {"border": "0.5px solid #e8e9eb"}}, {"label": "规格型号", "id": "u:4f53080ed9e2", "name": "jhiSpecification", "placeholder": "-", "quickEdit": false, "inline": true, "style": {"border": "0.5px solid #e8e9eb"}, "type": "text"}, {"label": "制剂规格", "name": "prepspec", "placeholder": "-", "quickEdit": false, "id": "u:b9a977da43ac", "inline": true, "style": {}, "type": "tpl", "width": 100, "tpl": "${prepspec|truncate:8}", "popOver": {"trigger": "hover", "position": "left-top", "showIcon": false, "body": [{"type": "tpl", "tpl": "${prepspec}"}]}, "themeCss": {"baseControlClassName": {"border:default": {"border": "0.5px solid #e8e9eb"}}}}, {"label": "经营属性", "id": "u:8b0d459ea1fe", "name": "goodsline", "placeholder": "-", "quickEdit": false, "inline": true, "style": {"border": "0.5px solid #e8e9eb"}, "type": "text"}, {"label": "产地", "id": "u:f2ece8655965", "name": "habitat", "placeholder": "-", "quickEdit": false, "inline": true, "style": {}, "type": "tpl", "width": 100, "tpl": "${habitat|truncate:6}", "popOver": {"trigger": "hover", "position": "left-top", "showIcon": false, "body": [{"type": "tpl", "tpl": "${habitat}", "id": "u:3dfc2d097994"}], "id": "u:504e5d17bb03"}, "themeCss": {"baseControlClassName": {"border:default": {"border": "0.5px solid #e8e9eb"}}}}, {"label": "计量单位", "id": "u:ee5315c0666a", "placeholder": "-", "name": "unit", "quickEdit": false, "inline": true, "style": {"border": "0.5px solid #e8e9eb"}}, {"type": "text", "label": "批发价", "id": "u:7da4eda80844", "placeholder": "-", "quickEdit": false, "name": "wholesalePrice", "inline": true, "style": {"border": "0.5px solid #e8e9eb"}}, {"type": "text", "label": "零售价", "id": "u:a343997ecb98", "placeholder": "-", "quickEdit": false, "name": "advicePrice", "inline": true, "style": {"border": "0.5px solid #e8e9eb"}}, {"label": "成本价", "type": "text", "placeholder": "-", "quickEdit": false, "id": "u:03949d0995c8", "name": "costPrice", "inline": true, "style": {"border": "0.5px solid #e8e9eb"}, "visibleOn": "${supperRole}"}, {"type": "text", "label": "最低限价", "placeholder": "-", "quickEdit": false, "id": "u:c3eb6e79b806", "name": "minPrice", "inline": true, "style": {"border": "0.5px solid #e8e9eb"}}, {"type": "tpl", "label": "促销价", "id": "u:12f53193e475", "placeholder": "-", "quickEdit": false, "tpl": "${ablePromo ? cartSettleprice : '-'}"}, {"type": "text", "label": "毛利额", "name": "grossAmount", "placeholder": "-", "quickEdit": false, "id": "u:e568fa099a41", "inline": true, "style": {"border": "0.5px solid #e8e9eb"}, "visibleOn": "${supperRole}"}, {"type": "text", "label": "毛利率", "id": "u:94f2f6c1d99f", "name": "grossRate", "placeholder": "-", "quickEdit": false, "inline": true, "style": {"border": "0.5px solid #e8e9eb"}, "visibleOn": "${supperRole}"}, {"type": "text", "label": "上次销售日期", "id": "u:07ea330f5f98", "name": "lastSaleDate", "placeholder": "-", "quickEdit": false, "inline": true, "style": {"border": "0.5px solid #e8e9eb"}}, {"type": "text", "label": "上次销售数量", "name": "lastSaleCount", "quickEdit": false, "id": "u:910f2720b0c2", "placeholder": "-", "inline": true, "style": {"border": "0.5px solid #e8e9eb"}}, {"type": "text", "label": "上次销价", "id": "u:b46b492404ad", "name": "lastSalePrice", "quickEdit": false, "placeholder": "-", "inline": true, "style": {"border": "0.5px solid #e8e9eb"}}, {"type": "text", "label": "无税金额", "id": "u:b509223b0713", "placeholder": "-", "name": "noTaxAmount", "quickEdit": false, "inline": true, "style": {"border": "0.5px solid #e8e9eb"}}, {"type": "tpl", "label": "错误信息", "id": "u:0d6640c64a20", "placeholder": "-", "name": "errorMsg", "quickEdit": false, "width": 180, "inline": true, "style": {}, "wrapperCustomStyle": {"display": "-webkit-box", "-webkit-box-orient": "vertical", "overflow": "hidden", "text-overflow": "ellipsis", "-webkit-line-clamp": "3"}}, {"type": "input-number", "label": "价格", "id": "u:63396784bb74", "fixed": "right", "placeholder": "", "name": "settleprice", "quickEdit": false, "width": 130, "size": "full", "inputClassName": "kp-cart-settleprice", "keyboard": false, "disabledOn": "${selfGoodsType == 1}", "step": 0.001, "onEvent": {"blur": {"weight": 0, "actions": [{"ignoreError": false, "actionType": "custom", "script": "console.error('失去焦点')\nconst newEventData = {\n  isCartEditUpdate: true,\n};\nlet errorMsg = \"\"\nlet currentContext = event.data;\nlet curInputPrice = event.data.value;\nlet goodsMold = currentContext.goodsMold\nlet readSettleprice = Number(currentContext.readSettleprice);\n\n// 价格：settleprice\n// 最低限价： minPrice\n// 最高限价：maxPrice\n// 成本价：costPrice\n// 批发价：wholesalePrice （目录价）\nlet settleprice = Number(currentContext.settleprice)\nlet minPrice = Number(currentContext.minPrice);\nlet maxPrice = Number(currentContext.maxPrice);\nlet costPrice = Number(currentContext.costPrice);\nlet wholesalePrice = Number(currentContext.wholesalePrice);\n\nconst wcMinPrice = Math.min(costPrice, wholesalePrice);\nconst wmMinPrice = Math.min(minPrice, wholesalePrice);\n\nconsole.log('===settleprice1', readSettleprice, curInputPrice, readSettleprice !== curInputPrice)\n// 默认初始化为非编辑状态\nwindow.KUAIPI_CART_ISEDITING = false;\n\n// 如果价格不一致，则购物车处于编辑状态\nif (readSettleprice != curInputPrice) {\n  window.KUAIPI_CART_ISEDITING = true;\n}\n// 如果价格一定就返回什么都不处理，直接进行订单提交\nif (readSettleprice == curInputPrice) {\n  // 此时购物车不处于编辑状态\n  newEventData.isCartEditUpdate = false;\n  event.setData({\n    ...event.data,\n    ...newEventData,\n  })\n  return;\n}\n// 锁价品\nif (goodsMold == 2 && curInputPrice < wholesalePrice) {\n  // 有最低限价和最高限价，且最低限价 = 最高限价 = 批发价,填写价格不能比批发价低\n  newEventData.isCartEditUpdate = false;\n  errorMsg = '锁价品填写值(' + curInputPrice + ')不能比批发价(' + wholesalePrice + ')低'\n} else if (!minPrice && costPrice && wholesalePrice && curInputPrice < wcMinPrice) {// 仅有批发价和成本价，不能低于两者的最小值\n  const wcDesc = costPrice === wcMinPrice ? '成本价' : '批发价'\n  newEventData.isCartEditUpdate = false;\n  errorMsg = `填写值(${curInputPrice})不能比${wcDesc}(${wcMinPrice})低`\n} else if (wholesalePrice && minPrice && curInputPrice < wmMinPrice) { // 包含批发价和最低限价，不能低于两者的最小值\n  const wmDesc = minPrice === wmMinPrice ? '最低限价' : '批发价'\n  newEventData.isCartEditUpdate = false;\n  errorMsg = `填写值(${curInputPrice})不能比${wmDesc}(${wmMinPrice})低`\n}\nconsole.error(errorMsg)\nif (!newEventData.isCartEditUpdate) {\n  doAction({\n    \"actionType\": \"toast\",\n    \"args\": {\n      \"msgType\": \"error\",\n      \"msg\": errorMsg,\n      \"position\": \"top-center\"\n    }\n  });\n  setTimeout(() => {\n    console.error('关闭弹窗');\n    doAction({\n      \"actionType\": \"closeDialog\",\n      \"componentId\": \"cart_editing_loading1\",\n      \"ignoreError\": true\n    })\n    doAction({\n      \"actionType\": \"closeDialog\",\n      \"componentId\": \"cart_editing_loading2\",\n      \"ignoreError\": true\n    })\n    // 清空购物车编辑状态\n    window.KUAIPI_CART_ISEDITING_SUBMIT = false;\n    window.KUAIPI_CART_ISEDITING_ZANCUN_SUBMIT = false;\n    window.KUAIPI_CART_ISEDITING = false;\n  }, 300)\n  doAction({\n    \"ignoreError\": false,\n    \"actionType\": \"reload\",\n    \"componentId\": \"shop_cart_id\"\n  })\n}\n\nevent.setData({\n  ...event.data,\n  ...newEventData,\n})", "args": {}}, {"actionType": "stopPropagation", "expression": "${!isCartEditUpdate}"}, {"actionType": "ajax", "ignoreError": false, "outputVar": "responseResult", "options": {"silent": false}, "api": {"url": "/purchase-data/api/cmall/cart", "method": "post", "requestAdaptor": "console.log('===api22', api)\nlet currentContext = api.context\nlet goodsMold = currentContext.goodsMold\nlet defaulySettleprice = currentContext.defaulySettleprice\nlet readSettleprice = currentContext.readSettleprice\nlet minPrice = currentContext.minPrice\nlet costPrice = currentContext.costPrice\nlet channelPrice = Number(currentContext.settleprice)\nlet wholesalePrice = currentContext.wholesalePrice\n\nconsole.log('===channelPrice1', channelPrice)\n\nlet data = {\n  handleType: 3,\n  businessId: currentContext.userBusinessId,\n  cartUpdateParam: {\n    itemId: currentContext.itemId,\n    businessId: currentContext.userBusinessId,\n    channelStoreId: currentContext.channelStoreId,\n    channelPrice: channelPrice,\n    count: currentContext.count,\n    id: currentContext.cartId,\n    orderId: currentContext.orderId,\n  }\n}\napi.data = data\nreturn api;", "adaptor": "console.log('修改价格payload', payload)\n\nreturn payload\n\n", "messages": {"success": ""}, "data": {"&": "$$"}, "responseData": {"&": "$$"}}}, {"ignoreError": false, "actionType": "custom", "script": "console.log('修改价格event', event)\nconsole.log('修改价格context', context)\n\nlet responseResult = event.data.responseResult\n\nwindow.KUAIPI_CART_ISEDITING = false;\n\nevent.setData({\n  ...event.data,\n  isCartEditingSubmit: window.KUAIPI_CART_ISEDITING_SUBMIT || window.KUAIPI_CART_ISEDITING_ZANCUN_SUBMIT\n})\n\ndoAction({\n  \"actionType\": \"closeDialog\",\n  \"componentId\": \"cart_editing_loading1\",\n  \"ignoreError\": true\n})\ndoAction({\n  \"actionType\": \"closeDialog\",\n  \"componentId\": \"cart_editing_loading2\",\n  \"ignoreError\": true\n})\n// 订单继续提交\nif ((window.KUAIPI_CART_ISEDITING_SUBMIT || window.KUAIPI_CART_ISEDITING_ZANCUN_SUBMIT) && responseResult && responseResult.payAmount) {\n  \n  if (window.KUAIPI_CART_ISEDITING_SUBMIT) {\n    const subAlts = document.querySelector('.submit_alt_s');\n    subAlts.click();\n  }\n  if (window.KUAIPI_CART_ISEDITING_ZANCUN_SUBMIT) {\n    const kp_zancun_hotkey = document.querySelector('.kp_zancun_hotkey')\n    kp_zancun_hotkey.click();\n  }\n}\nwindow.KUAIPI_CART_ISEDITING_SUBMIT = false;\nwindow.KUAIPI_CART_ISEDITING_ZANCUN_SUBMIT = false;\n// 如果修改购物车数据接口异常，禁止订单提交\nif (!responseResult) {\n  doAction({\n    \"componentId\": \"u:262a314ce704\",\n    \"ignoreError\": false,\n    \"actionType\": \"setValue\",\n    \"args\": {\n      \"value\": {\n        \"cartListDtoList\": []\n      }\n    }\n  });\n  return;\n};\ndoAction({\n  \"actionType\": \"setValue\",\n  \"componentId\": \"wholesale_supermarket_form\",\n  \"args\": {\n    \"value\": {\n      \"payAmount\": responseResult.payAmount,\n      \"grossAmount\": responseResult.grossAmount,\n      \"grossRate\": responseResult.grossRate\n    }\n  }\n})", "args": {}}, {"actionType": "stopPropagation", "expression": "${isCartEditingSubmit}"}, {"ignoreError": false, "actionType": "reload", "componentId": "shop_cart_id", "stopPropagation": ""}]}}, "showSteps": false, "big": true, "required": true, "precision": 5}, {"type": "input-number", "label": "数量", "id": "u:39334f2fe2f6", "fixed": "right", "placeholder": "", "name": "count", "quickEdit": false, "inline": true, "width": "", "size": "xs", "inputClassName": "kp-cart-count", "keyboard": false, "disabledOn": "${selfGoodsType == 1}", "step": 1, "static": false, "onEvent": {"blur": {"weight": 0, "actions": [{"ignoreError": false, "actionType": "custom", "script": "console.log('===event', event)\nlet currentContext = event.data;\nlet mpacking = currentContext.mpacking\nlet innerqty = currentContext.innerqty\nlet count = Number(currentContext.count)\nlet availableCount = currentContext.availableCount\nlet readCount = currentContext.readCount\n\nlet limitSplitRowCount = Math.abs(count - Number(readCount));\nconst newEventData = {\n  isCartEditUpdate: true,\n};\nlet limitCount = Math.floor(mpacking ? mpacking : innerqty)\nwindow.KUAIPI_CART_ISEDITING = false;\n// 如果数量不一致，则购物车处于编辑状态\nif (readCount != count) {\n  window.KUAIPI_CART_ISEDITING = true;\n}\n// 如果数量一直，则购物车不处于编辑状态\nif (readCount == count) {\n  // 此时购物车不处于编辑状态\n  window.KUAIPI_CART_ISEDITING = false;\n  newEventData.isCartEditUpdate = false;\n  event.setData({\n    ...event.data,\n    ...newEventData,\n  })\n  console.error('购物车数量编辑状态1', window.KUAIPI_CART_ISEDITING)\n  return;\n}\n\nif (count < 1 || count >= 40000 || count > availableCount) {\n  let errMsg = ''\n  if (count < 1) {\n    errMsg = \"填写值(\" + count + \")不能低于下限1\"\n  } else if (count >= 40000) {\n    errMsg = \"填写值(\" + count + \")不能大于等于上限40000\"\n  } else {\n    errMsg = \"填写值(\" + count + \")不能超过库存(\" + availableCount + \")\"\n  }\n  newEventData.isCartEditUpdate = false;\n  doAction({\n    \"actionType\": \"toast\",\n    \"args\": {\n      \"msgType\": \"error\",\n      \"msg\": errMsg,\n      \"position\": \"top-center\"\n    }\n  })\n} else if (limitCount && limitSplitRowCount % (limitCount + 0) != 0) {\n  newEventData.isCartEditUpdate = false;\n  doAction({\n    \"actionType\": \"toast\",\n    \"args\": {\n      \"msgType\": \"error\",\n      \"msg\": `商品[${currentContext.goodsName}]数量填写值不是最小中包装或者中包装(${limitCount})的整数倍`,\n      \"position\": \"top-center\"\n    }\n  })\n  \n}\nif (!newEventData.isCartEditUpdate) {\n  setTimeout(() => {\n    console.error('关闭弹窗');\n    doAction({\n      \"actionType\": \"closeDialog\",\n      \"componentId\": \"cart_editing_loading1\",\n      \"ignoreError\": true\n    })\n    doAction({\n      \"actionType\": \"closeDialog\",\n      \"componentId\": \"cart_editing_loading2\",\n      \"ignoreError\": true\n    })\n    // 清空购物车编辑状态\n    window.KUAIPI_CART_ISEDITING_SUBMIT = false;\n    window.KUAIPI_CART_ISEDITING_ZANCUN_SUBMIT = false;\n    window.KUAIPI_CART_ISEDITING = false;\n  }, 300)\n  doAction({\n    \"ignoreError\": false,\n    \"actionType\": \"reload\",\n    \"componentId\": \"shop_cart_id\"\n  })\n}\nconsole.error('购物车数量编辑状态2', window.KUAIPI_CART_ISEDITING)\nevent.setData({\n  ...event.data,\n  ...newEventData,\n})", "args": {}}, {"actionType": "stopPropagation", "expression": "${!isCartEditUpdate}"}, {"actionType": "ajax", "ignoreError": false, "outputVar": "responseResult", "options": {"silent": false}, "api": {"url": "/purchase-data/api/cmall/cart", "method": "post", "requestAdaptor": "console.log('===api', api)\nlet currentContext = api.context\nlet mpacking = currentContext.mpacking\nlet innerqty = currentContext.innerqty\nlet count = Number(currentContext.count)\nlet availableStock = currentContext.availableStock\nlet readCount = Number(currentContext.readCount);\nlet operateType = count > readCount ? 1 : 2\ncount = Math.abs(count - readCount);\nlet data = {\n  handleType: 3,\n  businessId: currentContext.userBusinessId,\n  cartUpdateParam: {\n    itemId: currentContext.itemId,\n    businessId: currentContext.userBusinessId,\n    channelStoreId: currentContext.channelStoreId,\n    channelPrice: currentContext.settleprice,\n    count: count,\n    oldCount: readCount,\n    operateType,\n    id: api.context.cartId,\n    orderId: currentContext.orderId,\n  }\n}\napi.data = data\nconsole.log('===api_output', api)\nreturn api;\n\n", "adaptor": "console.log('修改价格payload', payload)\n\nreturn payload", "messages": {"success": ""}, "data": {"&": "$$"}}}, {"ignoreError": false, "actionType": "custom", "script": "console.error('接口发送请求成功~');\nlet responseResult = event.data.responseResult\nevent.setData({\n  ...event.data,\n  isCartEditingSubmit: window.KUAIPI_CART_ISEDITING_SUBMIT || window.KUAIPI_CART_ISEDITING_ZANCUN_SUBMIT\n})\nwindow.KUAIPI_CART_ISEDITING = false;\ndoAction({\n  \"actionType\": \"closeDialog\",\n  \"componentId\": \"cart_editing_loading1\",\n  \"ignoreError\": true\n})\ndoAction({\n  \"actionType\": \"closeDialog\",\n  \"componentId\": \"cart_editing_loading2\",\n  \"ignoreError\": true\n})\n// 订单继续提交\nif ((window.KUAIPI_CART_ISEDITING_SUBMIT || window.KUAIPI_CART_ISEDITING_ZANCUN_SUBMIT) && responseResult && responseResult.payAmount) {\n  if (window.KUAIPI_CART_ISEDITING_SUBMIT) {\n    const subAlts = document.querySelector('.submit_alt_s');\n    subAlts.click();\n  }\n  if (window.KUAIPI_CART_ISEDITING_ZANCUN_SUBMIT) {\n    const kp_zancun_hotkey = document.querySelector('.kp_zancun_hotkey')\n    kp_zancun_hotkey.click();\n  }\n}\n\nwindow.KUAIPI_CART_ISEDITING_SUBMIT = false;\nwindow.KUAIPI_CART_ISEDITING_ZANCUN_SUBMIT = false;\n\n// 如果修改购物车数据接口异常，禁止订单提交\nif (!responseResult) {\n  doAction({\n    \"componentId\": \"u:262a314ce704\",\n    \"ignoreError\": false,\n    \"actionType\": \"setValue\",\n    \"args\": {\n      \"value\": {\n        \"cartListDtoList\": []\n      }\n    }\n  });\n  return;\n};\n\ndoAction({\n  \"actionType\": \"setValue\",\n  \"componentId\": \"wholesale_supermarket_form\",\n  \"args\": {\n    \"value\": {\n      \"payAmount\": responseResult.payAmount,\n      \"grossAmount\": responseResult.grossAmount,\n      \"grossRate\": responseResult.grossRate\n    }\n  }\n})", "args": {}, "stopPropagation": "${isCartEditingSubmit}"}, {"actionType": "stopPropagation", "expression": "${isCartEditingSubmit}"}, {"ignoreError": false, "actionType": "reload", "componentId": "shop_cart_id"}]}}, "max": "", "min": "", "showSteps": false, "big": true}, {"type": "text", "label": "金额", "id": "u:567ba8c7ce3c", "fixed": "right", "placeholder": "-", "name": "payAmount", "quickEdit": false, "inline": true, "width": ""}, {"type": "operation", "label": "操作", "size": "sm", "id": "u:8ba3cbd8504b", "buttons": [{"label": "删除", "type": "button", "id": "u:76354cb00d81", "onEvent": {"click": {"weight": 0, "actions": [{"ignoreError": false, "outputVar": "responseResult", "actionType": "ajax", "options": {}, "api": {"url": "/purchase-data/api/cmall/cart", "method": "post", "requestAdaptor": "console.log('===api', api)\nlet data = {\n  handleType: 2,\n  businessId: api.context.userBusinessId,\n  cartGoodsDeleteParam: {\n    orderId: api.context.orderId,\n    itemId: api.context.itemId,\n    delType: 1,\n    businessId: api.context.userBusinessId,\n    channelStoreId: api.context.channelStoreId,\n    promotionId: api.context.promotionId,\n    promotionType: api.context.promotionType,\n    promotionWay: api.context.promotionWay,\n    giftParentId: api.context.giftParentId,\n    giftId: api.context.giftId,\n    ruleId: api.context.ruleId,\n    selfGoodsType: api.context.selfGoodsType,\n    giftInfo: api.context.giftInfo,\n    ids: [\n      api.context.cartId\n    ]\n  }\n}\napi.data = data\nreturn api;", "adaptor": "", "messages": {}, "data": {"&": "$$"}}}, {"componentId": "shop_cart_id", "ignoreError": false, "actionType": "reload", "args": {"resetPage": true}}]}}, "level": "link", "size": "lg", "className": "cart-delete-hotkey", "disabled": false}, {"id": "u:d8bdac4405b4", "type": "button", "onEvent": {"click": {"weight": 0, "actions": [{"ignoreError": false, "actionType": "toast", "args": {"msgType": "error", "position": "top-right", "closeButton": true, "showIcon": true, "msg": "${errorMsg}", "title": "${goodsNo}_${goodsName}_${manufacturer}_错误信息", "className": "theme-toast-action-scope", "timeout": 10000}}]}}, "label": "错误", "level": "link", "visibleOn": "${errorMsg}", "size": "lg", "themeCss": {"className": {"font:default": {"color": "var(--colors-error-5)"}}}}], "fixed": "right", "placeholder": "-"}], "id": "shop_cart_table_id", "name": "filterCartListDtoList", "onEvent": {"rowClick": {"actions": [{"actionType": "custom", "ignoreError": false, "script": "window.selectRowCar_kpzs(event.data.index);\n"}]}}, "labelClassName": "", "placeholder": "暂无商品", "rowClassNameExpr": "${index % 2 ? 'bg-white' : 'bg-gray-100'}", "bordered": true, "addable": false, "footerAddBtn": {"label": "新增", "icon": "fa fa-plus"}, "strictMode": true, "minLength": 0, "showFooterAddBtn": false, "showTableAddBtn": false, "columnsTogglable": true, "mode": "normal", "showIndex": true, "needConfirm": false, "affixHeader": true, "lazyRenderAfter": 30}], "dsType": "api", "showErrorMsg": false}], "size": "none", "style": {"position": "static", "display": "block", "flex": "0 0 150px", "overflowX": "visible", "overflowY": "scroll", "flexBasis": "700px"}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:401cfcd25b85"}], "style": {"position": "relative", "rowGap": "10px", "columnGap": "10px", "flexWrap": "nowrap", "inset": "auto", "flexDirection": "column"}, "isFixedHeight": false, "isFixedWidth": false}], "className": "cart-hotkey", "collapsable": false, "messages": {}, "loadDataOnce": true, "matchFunc": "", "headerToolbar": [], "footerToolbar": [], "collapsed": false, "hiddenOn": "${isHideShopCart}", "header": {"type": "flex", "id": "u:df545b04783f", "style": {"position": "static", "rowGap": "10px", "columnGap": "10px", "display": "flex", "flexWrap": "nowrap", "overflowX": "visible", "overflowY": "visible", "alignItems": "center", "width": "100vw", "placeContent": "flex-start"}, "items": [{"type": "container", "body": [{"type": "tpl", "id": "u:1927b6633113", "value": "购物车", "wrapperComponent": "", "tpl": "", "inline": true}], "size": "none", "style": {"position": "static", "display": "block", "flex": "0 0 auto"}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:1c647568ce20"}, {"type": "container", "body": [{"type": "button", "label": "清空购物车(F3)", "id": "u:6b11df0b51e1", "onEvent": {"click": {"actions": [{"ignoreError": false, "actionType": "ajax", "outputVar": "responseResult", "options": {}, "api": {"url": "/purchase-data/api/cmall/cart", "method": "post", "requestAdaptor": "console.log('===api', api)\nlet itemIdList = api.data.cartListDtoList.map(row => {\n  return row.itemId\n})\nlet data = {\n  handleType: 5,\n  businessId: api.context.userBusinessId,\n  cartBatchDelParam: {\n    businessId: api.context.userBusinessId,\n    channelStoreId: api.context.channelStoreId,\n    itemIdList: itemIdList,\n    orderId: api.context.orderId\n  }\n}\nconsole.error(api, data, '=======11111');\napi.data = data\nreturn api;", "adaptor": "", "messages": {}, "data": {"&": "$$", "cartListDtoList": "$cartListDtoList"}}}, {"ignoreError": false, "actionType": "custom", "script": "doAction({\n  \"componentId\": \"search_cart_goods_box_id\",\n  \"actionType\": \"clear\"\n})\nsetTimeout(function () {\n  doAction({\n    \"ignoreError\": false,\n    \"actionType\": \"reload\",\n    \"componentId\": \"shop_cart_id\",\n    \"expression\": \"\"\n  })\n}, 100);\n", "args": {}}], "weight": 0}}, "themeCss": {"className": {"padding-and-margin:default": {"height": "1.5rem", "line-height": "1.5rem"}}}, "disabledOn": "${OR(!cartListDtoList, cartListDtoList.length ==0, !ableCommit)}", "tpl": "内容", "wrapperComponent": "", "hotKey": "F3", "visible": true, "confirmText": "是否清空购物车？", "confirmTitle": "提示"}], "size": "none", "style": {"position": "static", "display": "block", "flex": "0 0 150px", "flexBasis": "120px", "overflowX": "visible"}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:b1c079b07b6e"}, {"type": "container", "body": [{"type": "button", "label": "刷新购物车(F7)", "onEvent": {"click": {"actions": [{"ignoreError": false, "actionType": "custom", "script": "doAction({\n  \"componentId\": \"search_cart_goods_box_id\",\n  \"actionType\": \"clear\"\n})\nsetTimeout(function () {\n  doAction({\n    \"ignoreError\": false,\n    \"actionType\": \"reload\",\n    \"componentId\": \"shop_cart_id\",\n    \"expression\": \"\"\n  })\n}, 100);\n", "args": {}}]}}, "id": "u:b5043e56925e", "themeCss": {"className": {"padding-and-margin:default": {"height": "1.5rem", "line-height": "1.5rem"}}}, "hotKey": "F7", "disabledOn": "${!channelStoreId}"}], "size": "none", "style": {"position": "static", "display": "block", "flex": "0 0 150px", "flexBasis": "120px", "overflowX": "visible"}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:e1835e71ac8f"}, {"type": "container", "body": [{"type": "search-box", "id": "search_cart_goods_box_id", "name": "cartSearchKeyword", "enhance": false, "placeholder": "商品编码或者名称", "onEvent": {"search": {"weight": 0, "actions": [{"ignoreError": false, "script": "doAction({\n  \"componentId\": \"u:262a314ce704\",\n  \"ignoreError\": false,\n  \"actionType\": \"setValue\",\n  \"args\": {\n    \"value\": {\n      \"cartSearchKeyword\": \"${event.context.data.cartSearchKeyword}\",\n      \"canFilterFromCartList\": true\n    }\n  }\n});\nsetTimeout(function () {\n  doAction({\n    \"ignoreError\": false,\n    \"actionType\": \"reload\",\n    \"componentId\": \"shop_cart_id\",\n    \"expression\": \"\"\n  })\n}, 200);\n", "actionType": "custom", "args": {}}]}}, "searchImediately": true, "clearable": true, "clearAndSubmit": true, "mini": false}], "size": "none", "style": {"position": "static", "display": "block", "flex": "0 0 150px", "flexBasis": "160px", "overflowX": "visible"}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:ec0c4d52fdf3"}, {"type": "container", "body": [{"type": "tpl", "tpl": "<div>\n<div><span style=\"color: rgb(230, 0, 54);\">快捷键修改价格和数量改为enter！${currentEnv === 'stage' ? cartOrderId : ''}</span></div>\n</div>", "inline": true, "id": "u:f4b20ae99935", "themeCss": {"baseControlClassName": {"font:default": {"color": "#ff0000"}}}}], "size": "none", "style": {"position": "static", "display": "block", "flex": "0 0 auto"}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:80c72c878311", "themeCss": {"baseControlClassName": {"background:default": "rgba(230, 0, 54, 0.2)", "padding-and-margin:default": {"paddingTop": "var(--sizes-size-4)", "paddingRight": "var(--sizes-size-4)", "paddingBottom": "var(--sizes-size-4)", "paddingLeft": "var(--sizes-size-4)"}}}}], "isFixedHeight": false, "isFixedWidth": true, "stickyPosition": "auto", "stickyStatus": false}, "headingClassName": "absolute cart-header-kp"}, {"type": "flex", "id": "u:3e9ca2b1598d", "style": {"position": "sticky", "flexWrap": "nowrap", "rowGap": "10px", "columnGap": "10px", "height": "60px", "justifyContent": "space-between", "zIndex": 1000, "bottom": "0", "alignItems": "center", "overflowY": "visible", "inset": "auto auto 0px auto"}, "isFixedHeight": true, "isFixedWidth": false, "items": [{"type": "container", "body": [{"type": "tpl", "tpl": "<p>已加购 商品数<span style=\"color: rgb(186, 55, 42);\">${cartListDtoList ? cartListDtoList.length : 0}</span>件 &nbsp;&nbsp;&nbsp;&nbsp;实付金额：<span style=\"color: rgb(186, 55, 42);\">${totalPriceArea.payAmount ? totalPriceArea.payAmount : 0}</span></p>", "inline": true, "id": "u:ef105fa9d9ae", "editorSetting": {"mock": {}}, "themeCss": {"baseControlClassName": {"padding-and-margin:default": {"marginTop": "5px", "marginRight": "20px"}}}}, {"type": "tpl", "tpl": "<p>ABK商品实付金额：<span style=\"color: rgb(186, 55, 42);\">${totalPriceArea.abkAmount ? totalPriceArea.abkAmount : 0}</span></p>", "inline": true, "id": "u:2eabae6a5e90", "editorSetting": {"mock": {}}, "themeCss": {"baseControlClassName": {"padding-and-margin:default": {"marginTop": "5px", "marginRight": "20px"}}}}], "size": "none", "style": {"position": "static", "display": "flex", "flex": "1 1 auto", "flexGrow": 2, "flexBasis": "0px", "flexWrap": "nowrap", "justifyContent": "flex-start"}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:166e59af2e3e", "themeCss": {"baseControlClassName": {"padding-and-margin:default": {"paddingLeft": "var(--sizes-size-7)"}}}}, {"type": "container", "body": [{"type": "tpl", "id": "u:77b4aabd6098", "themeCss": {"baseControlClassName": {"padding-and-margin:default": {"marginTop": "5px", "marginRight": "20px"}}}, "tpl": "<p>成本金额：<span style=\"color: rgb(186, 55, 42);\">${totalPriceArea.costAmount ? totalPriceArea.costAmount : 0}</span>&nbsp;&nbsp;&nbsp;&nbsp;总利润额：<span style=\"color: rgb(186, 55, 42);\">${totalPriceArea.grossAmount ? totalPriceArea.grossAmount : 0}</span>&nbsp;&nbsp;&nbsp;&nbsp;总利润率：<span style=\"color: rgb(186, 55, 42);\">${totalPriceArea.grossRate ? totalPriceArea.grossRate : 0}</span></p>", "inline": true, "editorSetting": {"mock": {}}}], "size": "none", "style": {"position": "static", "display": "flex", "flex": "1 1 auto", "justifyContent": "flex-start", "flexWrap": "nowrap", "flexGrow": 2, "flexBasis": "0px"}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:9fc13a053f27", "themeCss": {"baseControlClassName": {"padding-and-margin:default": {"paddingLeft": "var(--sizes-size-7)"}}}}, {"type": "container", "body": [{"type": "button", "label": "取消", "onEvent": {"click": {"actions": [{"ignoreError": false, "actionType": "url", "args": {"url": "/purchase/amisPageV2/fLsGogGMXc", "blank": false}}]}}, "level": "default", "id": "u:d896217f491e", "themeCss": {"className": {"padding-and-margin:default": {"marginRight": "var(--sizes-size-5)"}}}}, {"type": "button", "label": "清空购物车(F6)", "onEvent": {"click": {"actions": [{"ignoreError": false, "actionType": "ajax", "outputVar": "responseResult", "options": {}, "api": {"url": "/purchase-data/api/cmall/cart", "method": "post", "requestAdaptor": "console.log('===api', api)\nlet itemIdList = api.data.cartListDtoList.map(row => {\n  return row.itemId\n})\nlet data = {\n  handleType: 5,\n  businessId: api.context.userBusinessId,\n  cartBatchDelParam: {\n    businessId: api.context.userBusinessId,\n    channelStoreId: api.context.channelStoreId,\n    itemIdList: itemIdList\n  }\n}\napi.data = data\nreturn api;", "adaptor": "", "messages": {}, "data": {"&": "$$", "cartListDtoList": "$cartListDtoList"}}}, {"componentId": "shop_cart_id", "ignoreError": false, "actionType": "reload"}], "weight": 0}}, "id": "u:64bdba6cfbd7", "themeCss": {"className": {"padding-and-margin:default": {"marginRight": "var(--sizes-size-5)"}}}, "disabledOn": "${OR(!cartListDtoList, cartListDtoList.length ==0, !ableCommit)}", "tpl": "内容", "wrapperComponent": "", "hotKey": "F6", "visible": false}, {"type": "button", "label": "提交并做下一单 (F9)", "onEvent": {"click": {"actions": [{"componentId": "is_next_order_id", "ignoreError": false, "actionType": "setValue", "args": {"value": "true"}}, {"componentId": "wholesale_supermarket_form", "ignoreError": false, "actionType": "submit", "outputVar": "submitResult"}, {"ignoreError": false, "script": "window.CLICK_ZANCUN_OR_SUBMIT = 3\n      ", "actionType": "custom"}]}}, "id": "u:203d54fba35c", "disabledOn": "${OR(!cartListDtoList, cartListDtoList.length ==0, !ableCommit)}", "themeCss": {"className": {"padding-and-margin:default": {"marginRight": "var(--sizes-size-5)"}}}, "className": "submit_next_order", "level": "primary", "visible": false}, {"type": "button", "label": "提交 (Alt+S)", "onEvent": {"click": {"actions": [{"actionType": "custom", "ignoreError": false, "script": "console.error('点击按钮', event)\nconsole.error('当前购物车是否正在编辑：', window.KUAIPI_CART_ISEDITING)\nevent.setData({\n  ...event.data,\n  isCartEditing: window.KUAIPI_CART_ISEDITING,\n})\n\nwindow.K<PERSON><PERSON>IPI_CART_ISEDITING_ZANCUN_SUBMIT = false;\n// 提交状态后续处理\nif (window.KUAIPI_CART_ISEDITING) {\n  window.KUAIPI_CART_ISEDITING_SUBMIT = true;\n}\nwindow.CLICK_ZANCUN_OR_SUBMIT = 2", "args": {}}, {"actionType": "dialog", "ignoreError": false, "dialog": {"type": "dialog", "title": "", "body": [{"type": "flex", "id": "u:f6f5090cfbaf", "items": [{"type": "container", "body": [{"type": "spinner", "overlay": false, "id": "u:d26ff58261b9", "body": [{"type": "tpl", "id": "u:f5f2b3c6c8cb", "wrapperCustomStyle": {}}]}], "size": "none", "style": {"position": "static", "display": "block", "flex": "1 1 auto", "flexGrow": 1, "flexBasis": "0px"}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:0b09bea8b7dc"}], "style": {"position": "relative", "rowGap": "10px", "columnGap": "10px", "flexWrap": "nowrap", "inset": "auto"}, "isFixedHeight": false, "isFixedWidth": false, "wrapperCustomStyle": {"padding": "10px 0"}}], "id": "cart_editing_loading1", "actions": [{"type": "tpl", "id": "u:600ef6c23fff", "tpl": "<p>当前购物车正在修改的数据未保存，保存中&middot;&middot;&middot;</p>", "wrapperCustomStyle": {"width": "100%", "text-align": "center"}, "inline": true}], "showCloseButton": false, "closeOnOutside": false, "closeOnEsc": false, "showErrorMsg": true, "showLoading": true, "draggable": false, "actionType": "dialog", "editorSetting": {"displayName": "购物车编辑状态"}, "size": "sm", "onEvent": {}}, "waitForAction": false, "expression": "${isCartEditing}", "stopPropagation": ""}, {"actionType": "stopPropagation", "expression": "${isCartEditing}"}, {"ignoreError": false, "actionType": "custom", "args": {}, "script": "console.error(event, '购物车正在修改的数据未保存，阻断')\nwindow.KUAIPI_CART_ISEDITING = false;\nwindow.KUAIPI_CART_ISEDITING_SUBMIT = false;\nwindow.KUAIPI_CART_ISEDITING_ZANCUN_SUBMIT = false;\n"}, {"ignoreError": false, "actionType": "dialog", "dialog": {"type": "dialog", "title": "", "body": [{"type": "flex", "id": "u:b7125f8614a7", "items": [{"type": "container", "body": [{"type": "spinner", "overlay": false, "id": "u:10ed4c4e4c0d", "body": [{"type": "tpl", "id": "u:79b4761fe4c8", "wrapperCustomStyle": {}}]}], "size": "none", "style": {"position": "static", "display": "block", "flex": "1 1 auto", "flexGrow": 1, "flexBasis": "0px"}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:8c75b55387d7"}], "style": {"position": "relative", "rowGap": "10px", "columnGap": "10px", "flexWrap": "nowrap", "inset": "auto"}, "isFixedHeight": false, "isFixedWidth": false, "wrapperCustomStyle": {"padding": "10px 0"}}], "id": "order_submit_loading", "actions": [{"type": "tpl", "id": "u:7ea9addacf40", "tpl": "订单提交中···", "wrapperCustomStyle": {"width": "100%", "text-align": "center"}, "inline": true}], "showCloseButton": false, "closeOnOutside": false, "closeOnEsc": true, "showErrorMsg": false, "showLoading": false, "draggable": false, "actionType": "dialog", "editorSetting": {"displayName": "提交loading"}, "size": "sm"}}, {"componentId": "u:f911b743cb07", "ignoreError": false, "actionType": "disabled"}, {"componentId": "u:294eaac97c6f", "ignoreError": false, "actionType": "disabled"}, {"componentId": "is_next_order_id", "ignoreError": false, "actionType": "setValue", "args": {"value": "false"}}, {"componentId": "shiftId_id", "ignoreError": false, "actionType": "setValue", "args": {"value": 1}}, {"componentId": "wholesale_supermarket_form", "ignoreError": false, "actionType": "submit", "outputVar": "submitResult"}]}}, "id": "u:f911b743cb07", "disabledOn": "${OR(!cartListDtoList, cartListDtoList.length ==0, !ableCommit)}", "themeCss": {"className": {"padding-and-margin:default": {"marginRight": "var(--sizes-size-5)"}}}, "className": "submit_alt_s"}, {"type": "button", "label": "暂存 (F8)", "onEvent": {"click": {"actions": [{"actionType": "custom", "ignoreError": false, "args": {}, "script": "console.error('点击按钮', event, window.KUAIPI_CART_ISEDITING)\nevent.setData({\n  ...event.data,\n  isCartEditing: window.KUA<PERSON>I_CART_ISEDITING,\n})\nwindow.KUAIPI_CART_ISEDITING_SUBMIT = false;\n// 提交状态后续处理\nif (window.KUA<PERSON>I_CART_ISEDITING) {\n  window.KUAIPI_CART_ISEDITING_ZANCUN_SUBMIT = true;\n}\nwindow.CLICK_ZANCUN_OR_SUBMIT = 1"}, {"ignoreError": false, "actionType": "dialog", "dialog": {"type": "dialog", "title": "", "body": [{"type": "flex", "items": [{"type": "container", "body": [{"type": "spinner", "overlay": false, "id": "u:728b626be3af", "body": [{"type": "tpl", "id": "u:bc0589fb8803", "wrapperCustomStyle": {}}]}], "size": "none", "style": {"position": "static", "display": "block", "flex": "1 1 auto", "flexGrow": 1, "flexBasis": "0px"}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:5edda999d023"}], "style": {"position": "relative", "rowGap": "10px", "columnGap": "10px", "flexWrap": "nowrap", "inset": "auto"}, "id": "u:cf1b387e9678", "isFixedHeight": false, "isFixedWidth": false, "wrapperCustomStyle": {"padding": "10px 0"}}], "id": "cart_editing_loading2", "actions": [{"type": "tpl", "tpl": "<p>当前购物车正在修改的数据未保存，保存中&middot;&middot;&middot;</p>", "id": "u:5341a90af54b", "wrapperCustomStyle": {"width": "100%", "text-align": "center"}, "inline": true}], "showCloseButton": false, "closeOnOutside": false, "closeOnEsc": false, "showErrorMsg": true, "showLoading": true, "draggable": false, "actionType": "dialog", "editorSetting": {"displayName": "购物车编辑状态"}, "size": "sm", "onEvent": {}}, "waitForAction": false, "expression": "${isCartEditing}", "stopPropagation": ""}, {"actionType": "stopPropagation", "expression": "${isCartEditing}"}, {"ignoreError": false, "actionType": "custom", "args": {}, "script": "console.error(event, '购物车正在修改的数据未保存，阻断')\nwindow.KUAIPI_CART_ISEDITING = false;\nwindow.KUAIPI_CART_ISEDITING_SUBMIT = false;\nwindow.KUAIPI_CART_ISEDITING_ZANCUN_SUBMIT = false;"}, {"ignoreError": false, "actionType": "dialog", "dialog": {"type": "dialog", "title": "", "body": [{"type": "flex", "items": [{"type": "container", "body": [{"type": "spinner", "overlay": false, "id": "u:55acac0c305d", "body": [{"type": "tpl", "id": "u:ed399830ede2", "wrapperCustomStyle": {}}]}], "size": "none", "style": {"position": "static", "display": "block", "flex": "1 1 auto", "flexGrow": 1, "flexBasis": "0px"}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:2a94bcaa4704"}], "style": {"position": "relative", "rowGap": "10px", "columnGap": "10px", "flexWrap": "nowrap", "inset": "auto"}, "id": "u:924afe67f876", "isFixedHeight": false, "isFixedWidth": false, "wrapperCustomStyle": {"padding": "10px 0"}}], "id": "order_submit_loading", "actions": [{"type": "tpl", "tpl": "暂存中···", "id": "u:3456504d5110", "wrapperCustomStyle": {"width": "100%", "text-align": "center"}, "inline": true}], "showCloseButton": false, "closeOnOutside": false, "closeOnEsc": true, "showErrorMsg": false, "showLoading": false, "draggable": false, "actionType": "dialog", "editorSetting": {"displayName": "暂存loading"}, "size": "sm"}}, {"componentId": "u:294eaac97c6f", "ignoreError": false, "actionType": "disabled"}, {"componentId": "u:f911b743cb07", "ignoreError": false, "actionType": "disabled"}, {"componentId": "is_next_order_id", "ignoreError": false, "actionType": "setValue", "args": {"value": "false"}}, {"componentId": "shiftId_id", "ignoreError": false, "actionType": "setValue", "args": {"value": 0}}, {"componentId": "wholesale_supermarket_form", "ignoreError": false, "actionType": "submit", "outputVar": "submitResult"}]}}, "id": "u:294eaac97c6f", "disabledOn": "${OR(!cartListDtoList, cartListDtoList.length ==0)}", "className": "kp_zancun_hotkey"}], "size": "none", "style": {"position": "static", "display": "flex", "flex": "0 0 auto", "justifyContent": "flex-end", "flexWrap": "nowrap"}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:0e2c019e8a92"}], "themeCss": {"baseControlClassName": {"background:default": "var(--colors-neutral-fill-11)", "border:default": {"top-border-style": "var(--borders-style-2)", "top-border-width": "var(--borders-width-2)", "top-border-color": "var(--colors-neutral-fill-8)"}}}, "originPosition": "right-bottom", "stickyPosition": "bottom", "stickyStatus": true}, {"type": "custom", "id": "u:0a9464726941", "html": "<div></div>", "onMount": "window.selectedRowIndex_kpzs = -1;\nwindow.selectedRowCarIndex_kpzs = -1;\nwindow.KUAIPI_CART_RETRY_TIMES = 0;\n// 购物车编辑状态\nwindow.KUAIPI_CART_ISEDITING_SUBMIT = false;\nwindow.KUAIPI_CART_ISEDITING_ZANCUN_SUBMIT = false;\nwindow.KUAIPI_CART_ISEDITING = false;\nwindow.KUAIPI_JIAGOU_isInsertCart = false;\n\n// 全局标记是点击暂存1，提交2，提交并下一单3，默认0\nwindow.CLICK_ZANCUN_OR_SUBMIT = 0\n\nconsole.error('购物车编辑状态初始化', window.KUAIPI_CART_ISEDITING_SUBMIT, window.KUAIPI_CART_ISEDITING_ZANCUN_SUBMIT, window.KUAIPI_CART_ISEDITING)\n\nlet purchase_quantity_selected = null\nlet cartHotKey = null\nlet gooodsSearchHotkey = null\n\nconst portalSiderToggle = document.querySelector('.portal-sider--toggle');\n\nportalSiderToggle && /is_open/.test(portalSiderToggle.className) && portalSiderToggle.click();\n\nfunction selectRow(index) {\n  const tbody = document.querySelector('.gooods_search_hotkey').querySelector('tbody');\n  tbody.querySelectorAll('.is-modified').forEach((e) => {\n    e.classList.remove('is-modified');\n  })\n  const rows = tbody.querySelectorAll('tr');\n  if (rows[window.selectedRowIndex_kpzs]) {\n    rows[window.selectedRowIndex_kpzs].classList.remove('is-modified');\n  }\n  window.selectedRowIndex_kpzs = index;\n  if (rows[window.selectedRowIndex_kpzs]) {\n    const purchase_quantity2 = rows[window.selectedRowIndex_kpzs].querySelector('.purchase_quantity input')\n    rows[window.selectedRowIndex_kpzs].classList.add('is-modified');\n    rows[window.selectedRowIndex_kpzs].scrollIntoView({ block: 'center' });\n    purchase_quantity2 && purchase_quantity2.focus();\n    purchase_quantity_selected = purchase_quantity2\n  }\n}\n\nfunction selectRowCar(index, type) {\n  const tbody = document.querySelector('.cart-hotkey').querySelector('tbody');\n  !type && document.activeElement.blur();\n  tbody.querySelectorAll('.is-modified').forEach((e) => {\n    e.classList.remove('is-modified');\n  })\n  const rows = tbody.querySelectorAll('tr');\n  if (rows[window.selectedRowCarIndex_kpzs]) {\n    rows[window.selectedRowCarIndex_kpzs].classList.remove('is-modified');\n  }\n\n  window.selectedRowCarIndex_kpzs = index;\n  if (rows[window.selectedRowCarIndex_kpzs]) {\n    rows[window.selectedRowCarIndex_kpzs].classList.add('is-modified');\n    rows[window.selectedRowCarIndex_kpzs].scrollIntoView({ block: 'center' });\n  }\n}\n\nwindow.selectRow_kpzs = selectRow\nwindow.selectRowCar_kpzs = selectRowCar\n\nconst search_goods_name_hotkey = document.querySelector('.search_goods_name_hotkey input')\n\n// 购物车鼠标进入事件\nwindow.cartHotKey_mouseenter_kpzs = function (event) {\n  cartHotKey = document.querySelector('.cart-hotkey')\n  purchase_quantity_selected && purchase_quantity_selected.blur();\n  gooodsSearchHotkey = null\n}\n\n// 商品搜索区进入事件\nwindow.gooodsSearchHotkey_mouseenter_kpzs = function (event) {\n  cartHotKey = null\n  gooodsSearchHotkey = document.querySelector('.gooods_search_hotkey');\n  if (purchase_quantity_selected && !purchase_quantity_selected.hasAttribute('disabled')) {\n    purchase_quantity_selected.focus();\n  } else {\n    search_goods_name_hotkey.focus();\n  }\n}\n\nsetTimeout(() => {\n  const kpselecthannel = document.querySelector('.kp-select-channel input')\n  kpselecthannel.focus();\n}, 0)\n\nwindow.visibilitychange_kpzs = function () {\n  if (document.visibilityState === 'visible') {\n    // console.log('document.visibilityState', document.visibilityState, '商品搜索框自动聚焦')\n    // search_goods_name_hotkey.focus();\n  }\n\n}\n\n// 创建一个Intersection Observer\nwindow.observer_scoll_kpzs = new IntersectionObserver((entries, observer) => {\n  entries.forEach(entry => {\n    if (entry.isIntersecting) {\n      // console.error(props, '元素进入可视区域');\n    } else {\n      // console.error('元素离开可视区域');\n      props.store.updateData({\n        isHideFormArea: true\n      })\n    }\n  });\n});\n\n// 购物车区域\nwindow.observer_cart_scoll_kpzs = new IntersectionObserver((entries, observer) => {\n  entries.forEach(entry => {\n    if (entry.isIntersecting) {\n      // console.error('observer_cart_scoll_kpzs', '元素进入可视区域');\n      // document.querySelector('.cart-hotkey').click()\n    } else {\n      // console.error('元素离开可视区域');\n      // props.store.updateData({\n      //   isHideFormArea: true\n      // })\n    }\n  });\n});\n\n// 商品搜索区域\nwindow.observer_goods_scoll_kpzs = new IntersectionObserver((entries, observer) => {\n  entries.forEach(entry => {\n    if (entry.isIntersecting) {\n      console.error('observer_goods_scoll_kpzs', '元素进入可视区域');\n      document.querySelector('.gooods_search_hotkey').click()\n    } else {\n      console.error('元素离开可视区域');\n    }\n  });\n});\n\n// 配置观察选项并开始观察\nwindow.observer_scoll_kpzs.observe(document.querySelector('.custom-client-hotkey'), {\n  root: null, // 使用浏览器视窗作为参照视窗\n  rootMargin: '120px'\n});\n// 配置观察选项并开始观察\n// window.observer_cart_scoll_kpzs.observe(document.querySelector('.cart-hotkey'), {\n//   root: null, // 使用浏览器视窗作为参照视窗\n//   rootMargin: '50px'\n// });\n// 配置观察选项并开始观察\nwindow.observer_goods_scoll_kpzs.observe(document.querySelector('.gooods_search_hotkey'), {\n  root: null, // 使用浏览器视窗作为参照视窗\n  rootMargin: '50px'\n});\nwindow.order_keydown_kpzs = function (event) {\n  let tbody = null\n  let table = null\n  let pagination = null;\n  let rows = null\n  const search_goods_name_hotkey = document.querySelector('.search_goods_name_hotkey input')\n  if (cartHotKey) {\n    tbody = document.querySelector('.cart-hotkey').querySelector('tbody');\n    table = document.querySelector('.cart-hotkey .cxd-Table-content');\n    pagination = document.querySelector('.cart-hotkey').querySelector('.cxd-Pagination-wrap')\n    rows = tbody.querySelectorAll('tr');\n  };\n\n  if (gooodsSearchHotkey) {\n    tbody = document.querySelector('.gooods_search_hotkey').querySelector('tbody');\n    table = document.querySelector('.gooods_search_hotkey .cxd-Table-content');\n    pagination = document.querySelector('.gooods_search_hotkey').querySelector('.cxd-Pagination-wrap')\n    rows = tbody.querySelectorAll('tr');\n  }\n\n  // Ctrl+Y:回到主页面(面包屑回退)\n  if (event.ctrlKey && event.key.toUpperCase() === 'Y') {\n    event.preventDefault();\n    window.history.go(-1);\n  }\n\n  if (event.key.toUpperCase() === 'F8') {\n    event.preventDefault();\n    const kpCount = document.activeElement && document.activeElement.closest('.kp-cart-count')\n    const kpCountSettleprice = document.activeElement && document.activeElement.closest('.kp-cart-settleprice')\n    if (kpCount || kpCountSettleprice) {\n      // 光标在此处，具体是否是编辑状态由内部判断\n      window.KUAIPI_CART_ISEDITING_ZANCUN_SUBMIT = true;\n      window.KUAIPI_CART_ISEDITING_SUBMIT = false;\n      document.activeElement.blur();\n    }\n    const kp_zancun_hotkey = document.querySelector('.kp_zancun_hotkey')\n    kp_zancun_hotkey.click();\n  }\n\n  if (event.key.toUpperCase() === 'F1') {\n    event.preventDefault();\n    search_goods_name_hotkey.focus();\n  }\n\n  if (event.altKey && event.code === 'KeyS') {\n    event.preventDefault();\n    const kpCount = document.activeElement && document.activeElement.closest('.kp-cart-count')\n    const kpCountSettleprice = document.activeElement && document.activeElement.closest('.kp-cart-settleprice')\n    if (kpCount || kpCountSettleprice) {\n      // 光标在此处，具体是否是编辑状态由内部判断\n      window.KUAIPI_CART_ISEDITING_ZANCUN_SUBMIT = false;\n      window.KUAIPI_CART_ISEDITING_SUBMIT = true;\n      document.activeElement.blur();\n    }\n    const subAlts = document.querySelector('.submit_alt_s');\n    subAlts.click();\n  }\n\n  if ((event.keyCode === 46 || event.keyCode === 8) && event.ctrlKey) {\n    event.preventDefault();\n    const removeCartEl = document.querySelector('.cart-hotkey tbody tr.is-modified .cart-delete-hotkey');\n    removeCartEl.click();\n  }\n\n  if (event.key === 'Enter' || event.keyCode === 13) {\n    console.error(document.activeElement);\n    const kpCount = document.activeElement && document.activeElement.closest('.kp-cart-count')\n    const kpCountSettleprice = document.activeElement && document.activeElement.closest('.kp-cart-settleprice')\n    if (kpCount || kpCountSettleprice) {\n      event.preventDefault();\n      document.activeElement.blur();\n    }\n  }\n\n  /************ 控制分页 start ************/\n  /// 上一页\n  if (event.ctrlKey && event.key == 'ArrowLeft') {\n    event.preventDefault();\n    const prevBtn = pagination.querySelector('.cxd-Pagination-prev');\n    console.error(prevBtn)\n    if (!!prevBtn) {\n      prevBtn.click();\n    }\n    return;\n  }\n  /// 下一页\n  if (event.ctrlKey && event.key == 'ArrowRight') {\n    event.preventDefault();\n    const nextBtn = pagination.querySelector('.cxd-Pagination-next');\n    console.error(nextBtn)\n    if (!!nextBtn) {\n      nextBtn.click();\n    }\n    return;\n  }\n  /************ 控制分页 end ************/\n\n  if (event.keyCode === 45 || event.key.toUpperCase() === 'F9') {\n    event.preventDefault();\n    const jiaGouPatch = document.querySelector('.goods_jiagou_hotkey_zc');\n    jiaGouPatch.click();\n  }\n\n  switch (event.key) {\n    case 'ArrowUp':\n      event.preventDefault();\n      if (cartHotKey) {\n        if (window.selectedRowCarIndex_kpzs > 0) {\n          selectRowCar(window.selectedRowCarIndex_kpzs - 1);\n        } else {\n          selectRow(window.selectedRowIndex_kpzs);\n        }\n      }\n      if (gooodsSearchHotkey) {\n        if (window.selectedRowIndex_kpzs > 0) {\n          selectRow(window.selectedRowIndex_kpzs - 1);\n        }\n      }\n\n      break;\n    case 'ArrowDown':\n      event.preventDefault();\n      if (cartHotKey) {\n        if (window.selectedRowCarIndex_kpzs < rows.length - 1) {\n          selectRowCar(window.selectedRowCarIndex_kpzs + 1);\n        }\n      }\n\n      if (gooodsSearchHotkey) {\n        if (window.selectedRowIndex_kpzs < rows.length - 1) {\n          selectRow(window.selectedRowIndex_kpzs + 1);\n        } else {\n          window.selectedRowCarIndex_kpzs = -1\n          selectRowCar(window.selectedRowCarIndex_kpzs + 1);\n        }\n      }\n\n      break;\n    case 'ArrowLeft':\n      if (document.activeElement === search_goods_name_hotkey) return;\n      event.preventDefault();\n      table.scrollLeft -= 20;\n      // Optional: Implement logic for left arrow key if needed\n      break;\n    case 'ArrowRight':\n      if (document.activeElement === search_goods_name_hotkey) return;\n      event.preventDefault();\n      table.scrollLeft += 20;\n      // Optional: Implement logic for right arrow key if needed\n      break;\n    default:\n      return; // Do nothing for other keys\n  }\n}\n\ndocument.addEventListener('visibilitychange', window.visibilitychange_kpzs);\ndocument.addEventListener('keydown', window.order_keydown_kpzs)\ndocument.querySelector('.cart-hotkey').addEventListener('mouseenter', window.cartHotKey_mouseenter_kpzs)\ndocument.querySelector('.gooods_search_hotkey').addEventListener('mouseenter', window.gooodsSearchHotkey_mouseenter_kpzs)\n", "onUnmount": "document.removeEventListener('keydown', window.order_keydown_kpzs)\ndocument.removeEventListener('visibilitychange', window.visibilitychange_kpzs)\ndocument.removeEventListener('mouseenter', window.cartHotKey_mouseenter_kpzs)\ndocument.removeEventListener('mouseenter', window.gooodsSearchHotkey_mouseenter_kpzs)\nwindow.observer_scoll_kpzs = null\nwindow.observer_goods_scoll_kpzs = null;\n// 清空各种全局状态\nwindow.KUAIPI_CART_RETRY_TIMES = 0;\n// 购物车编辑状态\nwindow.KUAIPI_CART_ISEDITING_SUBMIT = false;\nwindow.KUAIPI_CART_ISEDITING_ZANCUN_SUBMIT = false;\nwindow.KUAIPI_CART_ISEDITING = false;\nwindow.KUAIPI_JIAGOU_isInsertCart = false;\nconsole.error('页面销毁，购物车编辑状态重置', window.KUAIPI_CART_ISEDITING_SUBMIT, window.KUAIPI_CART_ISEDITING_ZANCUN_SUBMIT, window.KUAIPI_CART_ISEDITING)"}], "id": "u:262a314ce704", "pullRefresh": {"disabled": true}, "title": "新建订单", "toolbar": [{"type": "button", "id": "u:9092098f7c41", "label": "返回", "onEvent": {"click": {"actions": [{"actionType": "goBack", "ignoreError": false}]}}}], "definitions": {"modal-ref-2": {"type": "dialog", "title": "批次库存", "body": [{"type": "property", "title": "", "items": [{"label": "商品编码", "content": "${goodsNo}", "span": 1}, {"label": "商品名称", "content": "${goodsName}", "span": 2}, {"label": "可销库存", "content": "${availableStock}", "span": 1}], "id": "u:bb796f910137", "column": 4, "mode": "table"}, {"type": "crud", "syncLocation": false, "api": {"method": "post", "url": "/purchase-data/api/sap/stockDcList", "messages": {}, "requestAdaptor": "", "adaptor": "console.log('StockDcDetailDTO', payload)\nreturn payload", "data": {"&": "$$", "businessId": "$businessId", "goodsCode": "$goodsNo", "stockType": 3}}, "bulkActions": [], "itemActions": [], "columns": [{"name": "batchNo", "label": "生产批号", "type": "text", "id": "u:91a2c1a99e58", "placeholder": "-"}, {"type": "text", "label": "生产日期", "name": "produceDate", "id": "u:d2761c60527e", "placeholder": "-", "inline": true, "visible": true}, {"type": "text", "label": "有效期至/效期", "name": "expireDate", "id": "u:2996411e9159", "placeholder": "-", "inline": true, "visible": true}, {"type": "text", "label": "数量", "name": "stock", "id": "u:a2f3dc00265b", "placeholder": "-", "inline": true}], "id": "u:a0ddbe5f4e96", "perPageAvailable": [5, 10, 20, 50, 100], "messages": {}, "columnsTogglable": true, "footerToolbar": [{"type": "statistics", "align": "left"}, {"type": "pagination", "align": "right"}]}], "id": "u:737daab5f175", "actions": [{"type": "button", "actionType": "cancel", "label": "取消", "id": "u:47c68fadb4a5"}], "showCloseButton": true, "closeOnOutside": true, "closeOnEsc": true, "showErrorMsg": true, "showLoading": true, "draggable": false, "editorSetting": {"displayName": "batch_inventory"}, "actionType": "dialog", "size": "md", "hideActions": false, "$$ref": "modal-ref-2"}, "modal-ref-1": {"type": "dialog", "body": [{"type": "alert", "title": "", "body": [{"type": "tpl", "tpl": "抱歉，以下商品库存不足/不符合销售标准", "wrapperComponent": "", "inline": false, "id": "u:da5462243943"}], "level": "warning", "className": "mb-3", "id": "u:2c6eb157d39c"}, {"type": "crud", "syncLocation": false, "api": {"method": "get", "url": ""}, "bulkActions": [], "itemActions": [], "columns": [{"name": "goodsNo", "label": "商品编码", "type": "text", "id": "u:099e206babdc", "placeholder": "-"}, {"name": "skuName", "label": "商品名称", "type": "text", "id": "u:2ab56753bdd0", "placeholder": "-"}, {"name": "totalCount", "label": "购买数量", "type": "text", "id": "u:aedc1f769fec", "placeholder": "-"}, {"name": "skuCount", "label": "库存", "type": "text", "id": "u:8ce6a739a138", "placeholder": "-"}, {"name": "errorMsg", "label": "备注", "type": "text", "id": "u:8dc2e492d95b", "placeholder": "-"}], "id": "u:8c1d41ace1f1", "perPageAvailable": [5, 10, 20, 50, 100], "messages": {}, "source": "${event.data.goodsErrInfoList}"}], "title": "提示", "id": "u:c2f1238615ce", "actions": [{"type": "button", "label": "重新下单", "id": "u:41be55d31b3c", "level": "primary", "onEvent": {"click": {"actions": [{"ignoreError": false, "actionType": "url", "args": {"url": "/purchase/amisPageV2/xoSRLeSZSM?orderId=${orderNo}&orderStatusType='zc'&payType=${payType}&deliveryType=${deliveryTypeCode}", "blank": false}}]}}}], "showCloseButton": true, "closeOnOutside": false, "closeOnEsc": false, "showErrorMsg": true, "showLoading": true, "draggable": false, "size": "lg", "actionType": "dialog", "$$ref": "modal-ref-1"}}, "regions": ["body"], "css": {".cart-hotkey table tbody tr.is-modified td": {"background": "#dbeafe"}, ".cart-hotkey table tbody tr:hover": {"background": "#fff"}, ".gooods_search_hotkey table tbody tr:hover": {"background": "#fff"}, ".gooods_search_hotkey table tbody tr.is-modified td": {"background": "#dbeafe"}, ".gooods_search_hotkey .cxd-ColumnToggler-menu": {"max-height": "300px", "overflow-y": "scroll"}, ".cart-hotkey .cxd-ColumnToggler-menu": {"max-height": "220px", "overflow-y": "scroll", "right": "0", "left": "auto"}, ".cart-hotkey .cxd-Collapse-content": {"padding": "0"}, ".gooods_search_hotkey .cxd-Collapse-content": {"padding": "0"}, ".kpzs-font-weight-bold *": {"font-weight": "bold !important"}, ".text-gjred": {"color": "#e60036"}, ".gooods_search_hotkey .gj-Table-bordered table td": {"border-right": "1px solid #5a5a5a", "border-bottom": "1px solid #5a5a5a"}, ".gooods_search_hotkey .gj-Table-bordered table td.is-sticky-last-left": {"border-right": "0"}, ".gooods_search_hotkey .gj-Table-bordered table td.gj-kp-operation": {"border-right": "0px"}, ".gooods_search_hotkey .gj-Table-bordered table td.kp-godds-first-td": {"border-left": "1px solid #5a5a5a"}, ".gooods_search_hotkey .gj-Table-bordered .cxd-ColumnToggler .cxd-Modal": {"top": "-200px"}, ".cart-header-kp": {"z-index": "901", "width": "50%"}, ".cart-hotkey .cxd-Table-toolbar": {"justify-content": "flex-end"}, ".cart-hotkey .cxd-Table-toolbar .cxd-ColumnToggler": {"margin-right": "0"}, ".singlepcx_kp .cxd-Select-value": {"white-space": "pre-wrap"}, ".singlepcx_kp_overlay .cxd-Select-option": {"white-space": "pre-wrap", "height": "auto !important"}, ".gooods_search_hotkey .gj-Table-bordered .cxd-TransferDropDown-popover": {"max-width": "500px"}}, "themeCss": {"baseControlClassName": {"padding-and-margin:default": {"paddingBottom": "var(--sizes-size-0)"}}}, "onEvent": {"init": {"weight": 0, "actions": [{"ignoreError": false, "actionType": "custom", "args": {}, "script": "window.$qiankunBus.emit('changeTitle', {\n  title:'新建订单' })\n"}]}}, "className": "kpzs-font-weight-bold"}