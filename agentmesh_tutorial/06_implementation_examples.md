# AgentMesh多智能體協作系統實作示例

本文將提供完整的AgentMesh多智能體協作系統實作示例，從環境準備、配置文件編寫到代碼實現和運行流程，全面展示如何使用AgentMesh構建自定義的多智能體協作系統。通過本教程，您將能夠掌握AgentMesh的實際應用方法，並能夠根據自己的需求定制多智能體協作系統。

## 一、實作準備

在開始實作之前，我們需要完成環境準備和基本設置。

### 1. 環境準備

首先，確保您的系統滿足以下要求：

- Python 3.11+（推薦，尤其是使用瀏覽器工具時）
- Git（用於克隆代碼庫）
- 網絡連接（用於下載依賴和調用API）

### 2. 安裝AgentMesh

按照以下步驟安裝AgentMesh：

```bash
# 克隆AgentMesh代碼庫
git clone https://github.com/MinimalFuture/AgentMesh
cd AgentMesh

# 安裝核心依賴
pip install -r requirements.txt

# 如需使用瀏覽器工具，還需安裝額外依賴
pip install browser-use==0.1.40
playwright install
```

### 3. 獲取API密鑰

為了使用各種大型語言模型，您需要獲取相應的API密鑰：

- OpenAI API密鑰：[https://platform.openai.com/api-keys](https://platform.openai.com/api-keys)
- Claude API密鑰：[https://console.anthropic.com/settings/keys](https://console.anthropic.com/settings/keys)
- DeepSeek API密鑰：[https://platform.deepseek.com/api-keys](https://platform.deepseek.com/api-keys)
- 通義千問API密鑰：[https://dashscope.aliyun.com/apiKey](https://dashscope.aliyun.com/apiKey)

## 二、基本實作示例

我們將從一個基本的多智能體協作系統開始，逐步展示AgentMesh的使用方法。

### 1. 創建配置文件

首先，我們需要創建一個配置文件，定義模型和智能體團隊。

```bash
# 從模板創建配置文件
cp config-template.yaml config.yaml
```

編輯`config.yaml`文件，添加您的API密鑰和智能體團隊配置：

```yaml
# 模型配置
models:
  openai:
    api_key: "YOUR_OPENAI_API_KEY"  # 替換為您的OpenAI API密鑰
  claude:
    api_key: "YOUR_CLAUDE_API_KEY"  # 替換為您的Claude API密鑰
  deepseek:
    api_key: "YOUR_DEEPSEEK_API_KEY"  # 替換為您的DeepSeek API密鑰
  qwen:
    api_key: "YOUR_QWEN_API_KEY"  # 替換為您的通義千問API密鑰

# 智能體團隊配置
teams:
  research_team:
    description: "研究團隊，專注於信息收集、分析和報告生成"
    model: "gpt-4.1"  # 使用OpenAI的GPT-4.1模型
    provider: "openai"
    agents:
      researcher:
        description: "負責收集和整理信息"
        system_prompt: "你是一位專業的研究員，擅長使用搜索引擎和瀏覽器工具收集各類信息。你的任務是根據用戶的需求，收集相關的最新信息，並進行初步整理。"
        tools: ["google_search", "browser"]
      
      analyst:
        description: "負責分析信息並提供見解"
        system_prompt: "你是一位資深分析師，擅長從大量信息中提取關鍵點，識別趨勢和模式，並提供深入的分析和見解。你的任務是分析研究員提供的信息，找出關鍵趨勢和洞見。"
      
      writer:
        description: "負責撰寫最終報告"
        system_prompt: "你是一位專業的寫作者，擅長將複雜的信息轉化為清晰、結構化的報告。你的任務是根據研究員收集的信息和分析師提供的見解，撰寫一份全面、專業的報告。"
        tools: ["file_save"]
```

### 2. 運行基本示例

使用以下命令運行我們剛剛配置的研究團隊：

```bash
# 列出可用的智能體團隊
python main.py -l

# 使用研究團隊執行任務
python main.py -t research_team -q "分析人工智能在教育領域的最新應用和未來趨勢"
```

系統將啟動研究團隊，三個智能體將協作完成任務：
1. 研究員使用搜索引擎和瀏覽器工具收集關於AI在教育領域的最新信息
2. 分析師分析這些信息，提取關鍵趨勢和見解
3. 寫作者整合這些信息和見解，撰寫最終報告

### 3. 查看輸出結果

任務完成後，您可以在終端看到完整的輸出結果，包括：
- 智能體之間的對話過程
- 工具調用和結果
- 最終生成的報告

如果配置了`file_save`工具，寫作者還會將報告保存到本地文件中。

## 三、高級實作示例：自定義開發團隊

接下來，我們將創建一個更複雜的多智能體協作系統——自定義開發團隊，用於開發一個簡單的Web應用。

### 1. 配置自定義開發團隊

編輯`config.yaml`文件，添加自定義開發團隊配置：

```yaml
# 在teams部分添加
  dev_team:
    description: "開發團隊，專注於Web應用開發"
    model: "claude-sonnet-4-0"  # 使用Claude的模型
    provider: "claude"
    agents:
      product_manager:
        description: "負責產品需求和規劃"
        system_prompt: "你是一位經驗豐富的產品經理，擅長理解用戶需求並轉化為清晰的產品規格。你的任務是分析用戶需求，創建詳細的產品需求文檔(PRD)，包括功能描述、用戶故事、界面要求等。"
      
      ui_designer:
        description: "負責用戶界面設計"
        system_prompt: "你是一位專業的UI設計師，擅長創建美觀且用戶友好的界面設計。你的任務是根據產品經理提供的需求，設計Web應用的用戶界面，包括佈局、顏色方案、組件設計等。請提供詳細的設計描述和HTML/CSS代碼。"
      
      frontend_developer:
        description: "負責前端開發"
        system_prompt: "你是一位資深前端開發工程師，擅長使用HTML、CSS和JavaScript開發響應式Web應用。你的任務是根據UI設計師提供的設計，實現Web應用的前端部分，確保代碼清晰、高效且易於維護。"
        tools: ["calculator", "terminal", "file_save"]
      
      backend_developer:
        description: "負責後端開發"
        system_prompt: "你是一位資深後端開發工程師，擅長使用Python開發Web應用後端。你的任務是設計和實現Web應用的後端API和數據處理邏輯，確保系統穩定、安全且高效。"
        tools: ["calculator", "terminal", "file_save"]
      
      tester:
        description: "負責測試和質量保證"
        system_prompt: "你是一位細心的測試工程師，擅長發現問題並確保產品質量。你的任務是測試Web應用的各個功能，識別潛在問題，並提供詳細的測試報告。"
        tools: ["browser"]
```

### 2. 運行自定義開發團隊

使用以下命令運行我們配置的開發團隊：

```bash
# 使用開發團隊執行任務
python main.py -t dev_team -q "開發一個簡單的待辦事項管理Web應用，包括添加、編輯、刪除和標記完成待辦事項的功能"
```

系統將啟動開發團隊，五個智能體將協作完成Web應用開發：
1. 產品經理分析需求，創建產品需求文檔
2. UI設計師設計用戶界面
3. 前端開發者實現界面和交互
4. 後端開發者實現API和數據處理
5. 測試工程師測試應用功能

### 3. 保存和查看結果

開發團隊完成任務後，前端和後端開發者會使用`file_save`工具保存代碼文件。您可以在項目目錄中找到這些文件，並按照以下步驟運行Web應用：

```bash
# 創建一個目錄存放Web應用
mkdir -p todo_app
cd todo_app

# 將生成的代碼文件複製到該目錄
cp ../frontend.html ./index.html
cp ../backend.py ./app.py

# 安裝必要的依賴
pip install flask flask-cors

# 運行後端服務
python app.py
```

然後，在瀏覽器中打開`index.html`文件或訪問`http://localhost:5000`（如果後端服務配置了靜態文件服務）。

## 四、使用SDK進行編程式集成

除了通過配置文件和命令行運行，AgentMesh還提供了SDK，可以在Python代碼中直接使用。

### 1. 安裝SDK

```bash
pip install agentmesh-sdk
```

### 2. 基本SDK使用示例

創建一個Python文件`custom_team.py`，使用SDK創建和運行自定義團隊：

```python
from agentmesh import AgentTeam, Agent, LLMModel
from agentmesh.tools import Calculator, GoogleSearch, Browser, FileSystem

# 初始化模型
openai_model = LLMModel(model="gpt-4.1", api_key="YOUR_OPENAI_API_KEY")
claude_model = LLMModel(model="claude-sonnet-4-0", api_key="YOUR_CLAUDE_API_KEY")

# 創建團隊
team = AgentTeam(
    name="custom_research_team",
    description="自定義研究團隊",
    model=openai_model  # 團隊默認模型
)

# 添加智能體
team.add(Agent(
    name="Researcher",
    description="研究員",
    system_prompt="你是一位專業的研究員，擅長收集和分析信息。",
    tools=[GoogleSearch(), Browser()]
))

team.add(Agent(
    name="Writer",
    description="寫作者",
    system_prompt="你是一位專業的寫作者，擅長撰寫清晰、結構化的報告。",
    model=claude_model,  # 覆蓋團隊默認模型
    tools=[FileSystem()]
))

# 執行任務
result = team.run(task="研究並撰寫一份關於量子計算最新進展的報告")

# 打印結果
print(result)
```

運行此Python文件：

```bash
python custom_team.py
```

### 3. 高級SDK使用示例

創建一個更複雜的Python文件`advanced_team.py`，展示高級SDK功能：

```python
from agentmesh import AgentTeam, Agent, LLMModel
from agentmesh.tools import Calculator, GoogleSearch, Browser, FileSystem, Terminal, CurrentTime
import os

# 初始化模型
model = LLMModel(model="gpt-4.1", api_key="YOUR_OPENAI_API_KEY")

# 創建數據分析團隊
team = AgentTeam(
    name="data_analysis_team",
    description="數據分析團隊，專注於數據處理、分析和可視化",
    model=model
)

# 添加智能體
team.add(Agent(
    name="DataEngineer",
    description="數據工程師",
    system_prompt="""你是一位專業的數據工程師，擅長數據收集、清洗和預處理。
    你的任務是獲取數據，進行必要的清洗和轉換，準備好用於分析的數據集。
    請使用Python進行數據處理，優先使用pandas和numpy等庫。""",
    tools=[Terminal(), FileSystem(), Calculator()]
))

team.add(Agent(
    name="DataAnalyst",
    description="數據分析師",
    system_prompt="""你是一位資深數據分析師，擅長數據探索、統計分析和模式識別。
    你的任務是分析數據工程師準備的數據集，發現其中的趨勢、模式和洞見。
    請使用Python進行數據分析，優先使用pandas、scipy和statsmodels等庫。""",
    tools=[Terminal(), FileSystem(), Calculator()]
))

team.add(Agent(
    name="DataVisualizer",
    description="數據可視化專家",
    system_prompt="""你是一位專業的數據可視化專家，擅長創建清晰、信息豐富的數據可視化。
    你的任務是將數據分析師發現的洞見轉化為直觀的視覺表現。
    請使用Python進行數據可視化，優先使用matplotlib、seaborn和plotly等庫。""",
    tools=[Terminal(), FileSystem()]
))

team.add(Agent(
    name="ReportWriter",
    description="報告撰寫者",
    system_prompt="""你是一位專業的技術寫作者，擅長撰寫數據分析報告。
    你的任務是整合數據分析和可視化結果，撰寫一份全面、專業的分析報告。
    報告應包括執行摘要、方法論、發現、結論和建議等部分。""",
    tools=[FileSystem()]
))

# 創建工作目錄
os.makedirs("data_analysis_project", exist_ok=True)
os.chdir("data_analysis_project")

# 執行任務
task = """
分析過去5年全球可再生能源發展趨勢，重點關注：
1. 太陽能、風能、水能和生物質能的裝機容量變化
2. 各類可再生能源的成本變化趨勢
3. 主要國家和地區的可再生能源政策和投資
4. 未來5年的發展預測

請收集數據，進行分析，創建可視化，並撰寫一份全面的分析報告。
"""

result = team.run(task=task)

# 打印結果摘要
print("任務完成！")
print("生成的文件：")
for file in os.listdir("."):
    print(f" - {file}")
```

運行此Python文件：

```bash
python advanced_team.py
```

## 五、實作案例：自定義客戶服務團隊

接下來，我們將實現一個完整的客戶服務團隊案例，展示如何使用AgentMesh解決實際業務問題。

### 1. 需求分析

我們需要一個客戶服務團隊，能夠：
- 接收和理解客戶查詢
- 提供產品信息和技術支持
- 處理常見問題和投訴
- 升級複雜問題給專家
- 記錄客戶互動並生成報告

### 2. 團隊設計

我們將設計一個包含四個智能體的客戶服務團隊：
- 前台接待：接收初始查詢，分類問題
- 產品專家：提供產品信息和建議
- 技術支持：解決技術問題
- 客戶關係：處理投訴和敏感問題

### 3. 配置文件實現

創建或編輯`config.yaml`文件，添加客戶服務團隊配置：

```yaml
# 在teams部分添加
  customer_service_team:
    description: "客戶服務團隊，處理客戶查詢、技術支持和投訴"
    model: "gpt-4.1"
    provider: "openai"
    agents:
      receptionist:
        description: "前台接待，負責接收初始查詢並分類問題"
        system_prompt: """你是客戶服務團隊的前台接待，負責接收客戶的初始查詢並進行分類。
        你需要：
        1. 禮貌地歡迎客戶
        2. 理解客戶的問題
        3. 將問題分類為：產品信息、技術支持或投訴
        4. 收集必要的基本信息
        5. 將客戶引導至適合的專家
        
        請保持專業、友好的態度，確保客戶感到被重視。"""
      
      product_expert:
        description: "產品專家，提供產品信息和建議"
        system_prompt: """你是產品專家，負責提供準確的產品信息和專業建議。
        你需要：
        1. 了解公司所有產品的詳細信息
        2. 根據客戶需求推薦合適的產品
        3. 解答產品相關問題
        4. 提供產品比較和使用建議
        
        請確保提供的信息準確、全面，並根據客戶的具體需求進行個性化推薦。"""
        tools: ["google_search", "browser"]
      
      tech_support:
        description: "技術支持，解決技術問題"
        system_prompt: """你是技術支持專家，負責解決客戶遇到的技術問題。
        你需要：
        1. 理解客戶描述的技術問題
        2. 提供清晰的故障排除步驟
        3. 解釋技術概念，使客戶容易理解
        4. 在必要時提供進階技術支持
        
        請耐心指導客戶，使用簡明的語言解釋複雜的技術概念，確保客戶能夠成功解決問題。"""
        tools: ["google_search", "browser", "calculator"]
      
      customer_relations:
        description: "客戶關係專員，處理投訴和敏感問題"
        system_prompt: """你是客戶關係專員，負責處理客戶投訴和敏感問題。
        你需要：
        1. 認真傾聽客戶的不滿和擔憂
        2. 表達理解和同理心
        3. 提供解決方案和補償建議
        4. 確保客戶滿意度
        5. 記錄投訴詳情和處理結果
        
        請保持冷靜、專業，即使面對情緒激動的客戶也要有耐心，努力將負面體驗轉變為正面結果。"""
        tools: ["file_save"]
```

### 4. 運行客戶服務團隊

使用以下命令運行客戶服務團隊：

```bash
# 使用客戶服務團隊處理客戶查詢
python main.py -t customer_service_team -q "我最近購買的XYZ-1000智能家居系統無法連接到我的WiFi網絡，我已經嘗試重啟設備和路由器，但問題仍然存在。我對這個產品感到非常失望，因為我花了很多錢購買它。"
```

系統將啟動客戶服務團隊，四個智能體將協作處理客戶查詢：
1. 前台接待接收查詢，識別這是一個技術問題和潛在投訴
2. 技術支持提供WiFi連接問題的故障排除步驟
3. 產品專家提供產品相關信息和建議
4. 客戶關係專員處理客戶的失望情緒，提供補償建議

### 5. SDK實現版本

創建一個Python文件`customer_service.py`，使用SDK實現相同的客戶服務團隊：

```python
from agentmesh import AgentTeam, Agent, LLMModel
from agentmesh.tools import GoogleSearch, Browser, Calculator, FileSystem

# 初始化模型
model = LLMModel(model="gpt-4.1", api_key="YOUR_OPENAI_API_KEY")

# 創建客戶服務團隊
team = AgentTeam(
    name="customer_service_team",
    description="客戶服務團隊，處理客戶查詢、技術支持和投訴",
    model=model
)

# 添加智能體
team.add(Agent(
    name="Receptionist",
    description="前台接待，負責接收初始查詢並分類問題",
    system_prompt="""你是客戶服務團隊的前台接待，負責接收客戶的初始查詢並進行分類。
    你需要：
    1. 禮貌地歡迎客戶
    2. 理解客戶的問題
    3. 將問題分類為：產品信息、技術支持或投訴
    4. 收集必要的基本信息
    5. 將客戶引導至適合的專家
    
    請保持專業、友好的態度，確保客戶感到被重視。"""
))

team.add(Agent(
    name="ProductExpert",
    description="產品專家，提供產品信息和建議",
    system_prompt="""你是產品專家，負責提供準確的產品信息和專業建議。
    你需要：
    1. 了解公司所有產品的詳細信息
    2. 根據客戶需求推薦合適的產品
    3. 解答產品相關問題
    4. 提供產品比較和使用建議
    
    請確保提供的信息準確、全面，並根據客戶的具體需求進行個性化推薦。""",
    tools=[GoogleSearch(), Browser()]
))

team.add(Agent(
    name="TechSupport",
    description="技術支持，解決技術問題",
    system_prompt="""你是技術支持專家，負責解決客戶遇到的技術問題。
    你需要：
    1. 理解客戶描述的技術問題
    2. 提供清晰的故障排除步驟
    3. 解釋技術概念，使客戶容易理解
    4. 在必要時提供進階技術支持
    
    請耐心指導客戶，使用簡明的語言解釋複雜的技術概念，確保客戶能夠成功解決問題。""",
    tools=[GoogleSearch(), Browser(), Calculator()]
))

team.add(Agent(
    name="CustomerRelations",
    description="客戶關係專員，處理投訴和敏感問題",
    system_prompt="""你是客戶關係專員，負責處理客戶投訴和敏感問題。
    你需要：
    1. 認真傾聽客戶的不滿和擔憂
    2. 表達理解和同理心
    3. 提供解決方案和補償建議
    4. 確保客戶滿意度
    5. 記錄投訴詳情和處理結果
    
    請保持冷靜、專業，即使面對情緒激動的客戶也要有耐心，努力將負面體驗轉變為正面結果。""",
    tools=[FileSystem()]
))

# 處理客戶查詢
query = """我最近購買的XYZ-1000智能家居系統無法連接到我的WiFi網絡，我已經嘗試重啟設備和路由器，但問題仍然存在。我對這個產品感到非常失望，因為我花了很多錢購買它。"""

result = team.run(task=query)

# 打印結果
print(result)
```

運行此Python文件：

```bash
python customer_service.py
```

## 六、實作案例：多模態內容創作團隊

最後，我們將實現一個多模態內容創作團隊，展示AgentMesh處理複雜創意任務的能力。

### 1. 需求分析

我們需要一個內容創作團隊，能夠：
- 生成創意內容主題和大綱
- 撰寫高質量的文章內容
- 創建配圖和視覺元素
- 優化內容的SEO表現
- 整合所有元素生成最終內容

### 2. 團隊設計

我們將設計一個包含五個智能體的內容創作團隊：
- 創意總監：負責內容策略和創意方向
- 內容作家：負責撰寫主體內容
- 視覺設計師：負責創建配圖和視覺元素
- SEO專家：負責內容優化
- 編輯：負責整合和最終審核

### 3. 配置文件實現

創建或編輯`config.yaml`文件，添加內容創作團隊配置：

```yaml
# 在teams部分添加
  content_creation_team:
    description: "內容創作團隊，創建高質量的多模態內容"
    model: "claude-sonnet-4-0"
    provider: "claude"
    agents:
      creative_director:
        description: "創意總監，負責內容策略和創意方向"
        system_prompt: """你是內容創作團隊的創意總監，負責制定內容策略和創意方向。
        你需要：
        1. 理解目標受眾和內容目標
        2. 提出創新的內容主題和角度
        3. 制定內容結構和大綱
        4. 確保內容的獨特性和吸引力
        5. 指導團隊成員實現創意願景
        
        請發揮創造力，提出能夠吸引目標受眾的獨特內容創意。"""
      
      content_writer:
        description: "內容作家，負責撰寫主體內容"
        system_prompt: """你是專業的內容作家，負責撰寫高質量的主體內容。
        你需要：
        1. 根據創意總監提供的大綱撰寫內容
        2. 使用引人入勝的敘事和清晰的表達
        3. 確保內容準確、有深度且有價值
        4. 調整語調以適應目標受眾
        5. 遵循SEO最佳實踐
        
        請創作原創、有洞見且能引起共鳴的內容。"""
        tools: ["google_search", "browser", "file_save"]
      
      visual_designer:
        description: "視覺設計師，負責創建配圖和視覺元素"
        system_prompt: """你是視覺設計師，負責創建配圖和視覺元素。
        你需要：
        1. 理解內容主題和風格
        2. 提出視覺概念和設計方案
        3. 描述需要的圖像和視覺元素
        4. 提供詳細的設計規格和指南
        5. 確保視覺元素與內容協調一致
        
        請提出能夠增強內容吸引力和理解度的視覺設計方案。"""
      
      seo_specialist:
        description: "SEO專家，負責內容優化"
        system_prompt: """你是SEO專家，負責優化內容以提高搜索引擎排名。
        你需要：
        1. 研究相關關鍵詞和搜索趨勢
        2. 提供SEO標題和元描述建議
        3. 優化內容結構和格式
        4. 建議內部和外部鏈接策略
        5. 確保內容符合最新的SEO最佳實踐
        
        請提供能夠提高內容可見度和搜索排名的專業建議。"""
        tools: ["google_search", "browser"]
      
      editor:
        description: "編輯，負責整合和最終審核"
        system_prompt: """你是資深編輯，負責整合所有元素並進行最終審核。
        你需要：
        1. 審核和編輯文字內容
        2. 確保內容結構清晰且邏輯連貫
        3. 檢查事實準確性和語法錯誤
        4. 整合文字內容和視覺元素
        5. 確保最終內容符合質量標準和目標
        
        請確保最終內容專業、高質量且符合預期目標。"""
        tools: ["file_save"]
```

### 4. 運行內容創作團隊

使用以下命令運行內容創作團隊：

```bash
# 使用內容創作團隊創建內容
python main.py -t content_creation_team -q "為一家新成立的可持續時尚品牌創建一篇博客文章，主題是'可持續時尚如何改變消費者行為'，目標受眾是25-40歲的環保意識較強的都市消費者。"
```

系統將啟動內容創作團隊，五個智能體將協作創建內容：
1. 創意總監提出內容策略和大綱
2. 內容作家根據大綱撰寫主體內容
3. 視覺設計師提出配圖和視覺元素建議
4. SEO專家優化內容以提高搜索排名
5. 編輯整合所有元素並進行最終審核

### 5. SDK實現版本

創建一個Python文件`content_creation.py`，使用SDK實現相同的內容創作團隊：

```python
from agentmesh import AgentTeam, Agent, LLMModel
from agentmesh.tools import GoogleSearch, Browser, FileSystem
import os

# 初始化模型
model = LLMModel(model="claude-sonnet-4-0", api_key="YOUR_CLAUDE_API_KEY")

# 創建內容創作團隊
team = AgentTeam(
    name="content_creation_team",
    description="內容創作團隊，創建高質量的多模態內容",
    model=model
)

# 添加智能體
team.add(Agent(
    name="CreativeDirector",
    description="創意總監，負責內容策略和創意方向",
    system_prompt="""你是內容創作團隊的創意總監，負責制定內容策略和創意方向。
    你需要：
    1. 理解目標受眾和內容目標
    2. 提出創新的內容主題和角度
    3. 制定內容結構和大綱
    4. 確保內容的獨特性和吸引力
    5. 指導團隊成員實現創意願景
    
    請發揮創造力，提出能夠吸引目標受眾的獨特內容創意。"""
))

team.add(Agent(
    name="ContentWriter",
    description="內容作家，負責撰寫主體內容",
    system_prompt="""你是專業的內容作家，負責撰寫高質量的主體內容。
    你需要：
    1. 根據創意總監提供的大綱撰寫內容
    2. 使用引人入勝的敘事和清晰的表達
    3. 確保內容準確、有深度且有價值
    4. 調整語調以適應目標受眾
    5. 遵循SEO最佳實踐
    
    請創作原創、有洞見且能引起共鳴的內容。""",
    tools=[GoogleSearch(), Browser(), FileSystem()]
))

team.add(Agent(
    name="VisualDesigner",
    description="視覺設計師，負責創建配圖和視覺元素",
    system_prompt="""你是視覺設計師，負責創建配圖和視覺元素。
    你需要：
    1. 理解內容主題和風格
    2. 提出視覺概念和設計方案
    3. 描述需要的圖像和視覺元素
    4. 提供詳細的設計規格和指南
    5. 確保視覺元素與內容協調一致
    
    請提出能夠增強內容吸引力和理解度的視覺設計方案。"""
))

team.add(Agent(
    name="SEOSpecialist",
    description="SEO專家，負責內容優化",
    system_prompt="""你是SEO專家，負責優化內容以提高搜索引擎排名。
    你需要：
    1. 研究相關關鍵詞和搜索趨勢
    2. 提供SEO標題和元描述建議
    3. 優化內容結構和格式
    4. 建議內部和外部鏈接策略
    5. 確保內容符合最新的SEO最佳實踐
    
    請提供能夠提高內容可見度和搜索排名的專業建議。""",
    tools=[GoogleSearch(), Browser()]
))

team.add(Agent(
    name="Editor",
    description="編輯，負責整合和最終審核",
    system_prompt="""你是資深編輯，負責整合所有元素並進行最終審核。
    你需要：
    1. 審核和編輯文字內容
    2. 確保內容結構清晰且邏輯連貫
    3. 檢查事實準確性和語法錯誤
    4. 整合文字內容和視覺元素
    5. 確保最終內容符合質量標準和目標
    
    請確保最終內容專業、高質量且符合預期目標。""",
    tools=[FileSystem()]
))

# 創建工作目錄
os.makedirs("content_project", exist_ok=True)
os.chdir("content_project")

# 創建內容
task = """為一家新成立的可持續時尚品牌創建一篇博客文章，主題是'可持續時尚如何改變消費者行為'，目標受眾是25-40歲的環保意識較強的都市消費者。"""

result = team.run(task=task)

# 打印結果摘要
print("內容創作完成！")
print("生成的文件：")
for file in os.listdir("."):
    print(f" - {file}")
```

運行此Python文件：

```bash
python content_creation.py
```

## 七、實作總結與最佳實踐

通過以上實作示例，我們展示了如何使用AgentMesh構建各種多智能體協作系統。以下是一些關鍵的最佳實踐：

### 1. 智能體設計

- **明確職責**：為每個智能體定義清晰的職責範圍
- **專業提示詞**：編寫專業、詳細的系統提示詞，明確智能體的角色和任務
- **適當工具**：為智能體配置必要的工具，增強其能力
- **合適模型**：根據任務特點選擇適合的模型，平衡能力和成本

### 2. 團隊組建

- **互補角色**：設計互補的角色組合，覆蓋任務所需的所有專業領域
- **清晰協作流程**：定義智能體之間的協作流程和溝通方式
- **適當規模**：根據任務複雜度確定適當的團隊規模，避免過大或過小
- **靈活配置**：根據不同任務需求調整團隊組成和配置

### 3. 任務設計

- **明確目標**：提供明確、具體的任務目標和要求
- **適當背景**：提供必要的背景信息和上下文
- **合理範圍**：確保任務範圍合理，既不過於寬泛也不過於狹窄
- **清晰標準**：明確成功完成任務的標準和期望

### 4. 運行與監控

- **分步驗證**：對於複雜任務，分步驗證中間結果
- **適時干預**：在必要時進行人工干預和指導
- **結果評估**：評估最終結果的質量和符合度
- **持續優化**：根據運行結果不斷優化團隊配置和提示詞

## 參考資料

1. AgentMesh官方文檔：[https://docs.link-ai.tech/blog/agentmesh](https://docs.link-ai.tech/blog/agentmesh)
2. AgentMesh GitHub倉庫：[https://github.com/MinimalFuture/AgentMesh](https://github.com/MinimalFuture/AgentMesh)
3. AgentMesh SDK文檔：[https://agentmesh-sdk.readthedocs.io/](https://agentmesh-sdk.readthedocs.io/)
4. 多智能體協作機制設計及實踐：[https://blog.csdn.net/weixin_43990004/article/details/143512430](https://blog.csdn.net/weixin_43990004/article/details/143512430)
5. AgentMesh開源多智能體協作框架：[https://www.cnblogs.com/zhayujie/p/18857116](https://www.cnblogs.com/zhayujie/p/18857116)
