# AgentMesh官方文檔與權威資源彙總

## 官方資源

### 1. 官方GitHub倉庫
- **地址**：[https://github.com/MinimalFuture/AgentMesh](https://github.com/MinimalFuture/AgentMesh)
- **內容**：包含完整源碼、配置模板、安裝指南和使用說明
- **最新版本**：0.1.3（2025年5月23日發布）
- **授權協議**：Apache-2.0

### 2. 官方文檔
- **地址**：[https://docs.link-ai.tech/blog/agentmesh](https://docs.link-ai.tech/blog/agentmesh)
- **內容**：詳細介紹AgentMesh的背景、架構設計、運行流程和功能規劃
- **更新日期**：2025年5月5日

## 安裝與運行方式

AgentMesh提供三種主要的使用方式：

### 1. 終端運行
```bash
# 克隆倉庫
git clone https://github.com/MinimalFuture/AgentMesh
cd AgentMesh

# 安裝核心依賴
pip install -r requirements.txt

# 如需使用瀏覽器工具，還需安裝額外依賴（需要Python 3.11+）
pip install browser-use==0.1.40
playwright install

# 配置
cp config-template.yaml config.yaml
# 編輯config.yaml，添加模型API密鑰

# 運行（命令行參數方式）
python main.py -t general_team -q "analyze the trends in multi-agent technology"

# 運行（交互模式）
python main.py -l                               # 列出可用的智能體團隊
python main.py -t software_team                 # 運行指定團隊
```

### 2. Docker運行
```bash
# 下載docker-compose配置文件
curl -O https://raw.githubusercontent.com/MinimalFuture/AgentMesh/main/docker-compose.yml

# 下載配置模板
curl -o config.yaml https://raw.githubusercontent.com/MinimalFuture/AgentMesh/main/config-template.yaml
# 編輯config.yaml，添加模型API密鑰

# 運行Docker容器
docker-compose run --rm agentmesh bash

# 在容器內運行（與終端運行方式相同）
python main.py -l                               # 列出可用的智能體團隊
python main.py -t general_team                  # 運行指定團隊
```

### 3. SDK集成
```bash
# 安裝SDK
pip install agentmesh-sdk

# 示例用法
from agentmesh import AgentTeam, Agent, LLMModel
from agentmesh.tools import *

# 初始化模型
model = LLMModel(model="gpt-4.1", api_key="YOUR_API_KEY")

# 創建團隊並添加智能體
team = AgentTeam(name="software_team", description="A software development team", model=model)

team.add(Agent(name="PM", description="Handles product requirements",
               system_prompt="You are an experienced PM who creates clear, comprehensive PRDs"))

team.add(Agent(name="Developer", description="Implements code based on requirements", model=model,
               system_prompt="You write clean, efficient, maintainable code following requirements precisely",
               tools=[Calculator(), GoogleSearch()]))

# 執行任務
result = team.run(task="Write a Snake client game")
```

## 系統要求

- **操作系統**：支持Linux、MacOS、Windows
- **Python版本**：
  - 推薦：Python 3.11+（尤其是使用瀏覽器工具時）
  - 最低要求：Python 3.7+
- **下載地址**：[Python官網](https://www.python.org/)

## 支持的模型

AgentMesh支持多種大型語言模型：

- **OpenAI**：GPT系列模型，推薦：`gpt-4.1`、`gpt-4o`、`gpt-4.1-mini`
- **Claude**：Claude系列模型，推薦：`claude-sonnet-4-0`、`claude-3-7-sonnet-latest`
- **DeepSeek**：DeepSeek系列模型，推薦：`deepseek-chat`
- **Qwen**：通義千問系列模型
- **Ollama**：本地開源模型（即將推出）

## 內置工具

AgentMesh提供多種內置工具，擴展智能體的能力：

- **calculator**：數學計算工具，支持複雜表達式求值
- **current_time**：當前時間獲取工具，解決模型時間感知問題
- **browser**：基於browser-use的網頁瀏覽工具，支持網頁訪問、內容提取和交互
- **google_search**：搜索引擎工具，用於檢索最新信息
- **file_save**：將智能體輸出保存到本地工作區的工具
- **terminal**：命令行工具，用於安全執行系統命令，具有安全限制
- **MCP**：通過MCP協議支持的擴展工具能力（即將推出）

## 預置團隊模板

配置模板中預置了兩個示例團隊：

- **general_team**：通用智能體，適用於搜索和研究任務
- **software_team**：開發團隊，包含產品、工程和測試三個角色，可通過協作開發web網站，交付完整的項目代碼和文檔

## 核心概念

- **Agent**：具有特定角色和能力的自主決策單元，可配置模型、系統提示詞、工具和決策邏輯
- **AgentTeam**：負責任務分配、上下文管理和協作工作流的智能體團隊
- **Tool**：擴展智能體能力的功能模塊，如計算器、搜索引擎和瀏覽器
- **Task**：用戶輸入的問題或需求，可以包括文本、圖像和其他多模態內容
- **Context**：共享信息，包括團隊詳情、任務內容和執行歷史
- **LLMModel**：通過統一API支持各種主流LLM的大型語言模型接口

## 後續規劃

AgentMesh團隊計劃在未來推出以下功能：

- **WebUI**：用戶友好的多Agent管理和追蹤界面
- **支持MCP協議**：獲得無限擴展的工具能力
- **異構Agent通信**：支持打通不同Agent平台，與遠程Agent共同協作
- **更多模型**：支持更多模型廠商及本地開源模型

## 參考資料

1. AgentMesh GitHub倉庫：[https://github.com/MinimalFuture/AgentMesh](https://github.com/MinimalFuture/AgentMesh)
2. AgentMesh官方文檔：[https://docs.link-ai.tech/blog/agentmesh](https://docs.link-ai.tech/blog/agentmesh)
3. Lyzr AgentMesh架構介紹：[https://www.lyzr.ai/blog/lyzr-introduces-agentmesh-architecture/](https://www.lyzr.ai/blog/lyzr-introduces-agentmesh-architecture/)
