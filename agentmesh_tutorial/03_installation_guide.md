# AgentMesh安裝與環境設置詳細步驟

本文檔提供了AgentMesh多智能體協作系統的完整安裝與環境設置指南，包括三種不同的運行方式：終端運行、Docker運行和SDK集成。無論您是開發者、研究人員還是企業用戶，都可以根據自己的需求選擇最適合的安裝方式。

## 環境準備

在開始安裝AgentMesh之前，需要確保您的系統滿足以下基本要求：

### 系統要求

- **操作系統**：支持Linux、MacOS、Windows
- **Python版本**：
  - **推薦**：Python 3.11+（尤其是使用瀏覽器工具時）
  - **最低要求**：Python 3.7+
- **網絡連接**：安裝過程需要從網絡下載依賴包和模型
- **存儲空間**：建議至少有1GB可用空間（包括依賴庫和配置文件）

### Python環境安裝

如果您尚未安裝Python，請按照以下步驟進行安裝：

1. 訪問Python官網：[https://www.python.org/downloads/](https://www.python.org/downloads/)
2. 下載適合您操作系統的Python 3.11+版本
3. 運行安裝程序，並確保勾選「Add Python to PATH」選項
4. 安裝完成後，打開終端（命令提示符/PowerShell/Terminal）驗證安裝：

```bash
python --version  # 或 python3 --version
pip --version     # 或 pip3 --version
```

確認顯示的Python版本為3.7+（推薦3.11+）。

## 方式一：終端運行

終端運行是最直接的使用方式，適合開發者和研究人員快速開始使用AgentMesh。

### 步驟1：克隆GitHub倉庫

打開終端，執行以下命令克隆AgentMesh倉庫並進入項目目錄：

```bash
git clone https://github.com/MinimalFuture/AgentMesh
cd AgentMesh
```

### 步驟2：安裝核心依賴

在項目目錄中，執行以下命令安裝核心依賴：

```bash
pip install -r requirements.txt
```

如果您使用的是Python 3，可能需要使用`pip3`代替`pip`：

```bash
pip3 install -r requirements.txt
```

### 步驟3：安裝瀏覽器工具（可選）

如果您需要使用瀏覽器工具（例如網頁搜索、內容提取等功能），還需要安裝額外的依賴。注意，這一步驟需要Python 3.11+：

```bash
pip install browser-use==0.1.40
playwright install
```

### 步驟4：配置模型和智能體

AgentMesh需要配置文件來定義使用的模型和智能體團隊。首先，從模板創建配置文件：

```bash
cp config-template.yaml config.yaml
```

然後使用文本編輯器打開`config.yaml`文件，填寫您的模型API密鑰和其他配置：

```yaml
# 模型配置
models:
  openai:
    api_key: "YOUR_OPENAI_API_KEY"  # 替換為您的OpenAI API密鑰
  claude:
    api_key: "YOUR_CLAUDE_API_KEY"  # 替換為您的Claude API密鑰
  deepseek:
    api_key: "YOUR_DEEPSEEK_API_KEY"  # 替換為您的DeepSeek API密鑰
  qwen:
    api_key: "YOUR_QWEN_API_KEY"  # 替換為您的通義千問API密鑰

# 智能體團隊配置
teams:
  # 配置模板中已包含general_team和software_team，可以根據需要修改或添加自定義團隊
```

配置文件中預置了兩個示例團隊：
- `general_team`：通用智能體，適用於搜索和研究任務
- `software_team`：開發團隊，包含產品、工程和測試三個角色，可協作開發web網站

您可以根據需要修改這些團隊的配置，或添加自定義團隊。

### 步驟5：運行AgentMesh

AgentMesh提供兩種運行方式：命令行參數模式和交互模式。

#### 命令行參數模式

使用命令行參數直接執行任務，適合單次查詢或自動化腳本：

```bash
# 使用general_team執行任務
python main.py -t general_team -q "分析多智能體技術的發展趨勢"

# 使用software_team執行任務
python main.py -t software_team -q "開發一個簡單的AgentMesh多智能體平台預約頁面"
```

#### 交互模式

進入交互模式，可以進行多輪對話：

```bash
# 列出所有可用的智能體團隊
python main.py -l

# 啟動指定的智能體團隊
python main.py -t general_team
```

在交互模式下，您可以輸入問題並與智能體團隊進行多輪對話，輸入`exit`或`quit`退出交互模式。

## 方式二：Docker運行

Docker運行方式適合希望在隔離環境中使用AgentMesh或在服務器上部署的用戶。

### 步驟1：安裝Docker

如果您尚未安裝Docker，請按照以下步驟進行安裝：

1. 訪問Docker官網：[https://www.docker.com/get-started](https://www.docker.com/get-started)
2. 下載並安裝適合您操作系統的Docker Desktop或Docker Engine
3. 安裝完成後，打開終端驗證安裝：

```bash
docker --version
docker-compose --version
```

### 步驟2：下載Docker配置文件

打開終端，執行以下命令下載AgentMesh的Docker配置文件：

```bash
curl -O https://raw.githubusercontent.com/MinimalFuture/AgentMesh/main/docker-compose.yml
```

### 步驟3：下載並配置config.yaml

下載配置模板並創建配置文件：

```bash
curl -o config.yaml https://raw.githubusercontent.com/MinimalFuture/AgentMesh/main/config-template.yaml
```

使用文本編輯器打開`config.yaml`文件，填寫您的模型API密鑰和其他配置，與終端運行方式的配置相同。

### 步驟4：運行Docker容器

執行以下命令啟動AgentMesh Docker容器：

```bash
docker-compose run --rm agentmesh bash
```

這將啟動一個交互式bash會話，您可以在其中運行AgentMesh命令。

### 步驟5：在容器內運行AgentMesh

在Docker容器內，您可以使用與終端運行方式相同的命令運行AgentMesh：

```bash
# 列出所有可用的智能體團隊
python main.py -l

# 啟動指定的智能體團隊
python main.py -t general_team

# 使用命令行參數執行任務
python main.py -t software_team -q "開發一個簡單的網頁應用"
```

完成後，輸入`exit`退出容器。

## 方式三：SDK集成

SDK集成方式適合希望將AgentMesh集成到自己的Python應用程序中的開發者。

### 步驟1：安裝AgentMesh SDK

打開終端，執行以下命令安裝AgentMesh SDK：

```bash
pip install agentmesh-sdk
```

### 步驟2：在Python代碼中使用SDK

在您的Python應用程序中，可以使用以下代碼示例來集成AgentMesh：

```python
from agentmesh import AgentTeam, Agent, LLMModel
from agentmesh.tools import *

# 初始化模型（替換為您的API密鑰）
model = LLMModel(model="gpt-4.1", api_key="YOUR_API_KEY")

# 創建智能體團隊
team = AgentTeam(name="custom_team", description="自定義智能體團隊", model=model)

# 添加智能體
team.add(Agent(name="研究員", 
               description="負責收集和分析信息", 
               system_prompt="你是一位專業的研究員，擅長收集和分析各類信息，提供深入的見解和建議。"))

team.add(Agent(name="開發者", 
               description="負責實現代碼和技術方案", 
               model=model,
               system_prompt="你是一位經驗豐富的開發者，能夠編寫清晰、高效、可維護的代碼，並提供技術解決方案。",
               tools=[Calculator(), GoogleSearch()]))

# 執行任務
result = team.run(task="分析並實現一個簡單的多智能體協作系統")

# 處理結果
print(result)
```

您可以根據需要自定義智能體的名稱、描述、系統提示詞和工具，以及團隊的組成和任務。

## 配置文件詳解

AgentMesh的配置文件（`config.yaml`）是系統運行的核心，包含模型配置和智能體團隊配置兩部分。

### 模型配置

模型配置部分定義了可用的大型語言模型及其API密鑰：

```yaml
models:
  openai:
    api_key: "YOUR_OPENAI_API_KEY"
    # 可選：指定默認模型
    # model: "gpt-4.1"
  claude:
    api_key: "YOUR_CLAUDE_API_KEY"
    # model: "claude-sonnet-4-0"
  deepseek:
    api_key: "YOUR_DEEPSEEK_API_KEY"
  qwen:
    api_key: "YOUR_QWEN_API_KEY"
```

### 智能體團隊配置

智能體團隊配置部分定義了可用的智能體團隊及其組成：

```yaml
teams:
  general_team:
    description: "通用智能體團隊，適用於搜索和研究任務"
    model: "gpt-4.1"  # 團隊默認使用的模型
    provider: "openai"  # 模型提供商
    agents:
      researcher:
        description: "負責收集和分析信息"
        system_prompt: "你是一位專業的研究員，擅長收集和分析各類信息，提供深入的見解和建議。"
        tools: ["google_search", "browser"]  # 可用工具列表
      
  software_team:
    description: "軟件開發團隊，包含產品、工程和測試三個角色"
    model: "claude-sonnet-4-0"
    provider: "claude"
    agents:
      product_manager:
        description: "負責產品需求和規劃"
        system_prompt: "你是一位經驗豐富的產品經理，負責創建清晰、全面的產品需求文檔。"
      
      engineer:
        description: "負責代碼實現"
        system_prompt: "你是一位優秀的工程師，能夠編寫清晰、高效、可維護的代碼，並提供技術解決方案。"
        tools: ["calculator", "terminal", "file_save"]
      
      tester:
        description: "負責測試和質量保證"
        system_prompt: "你是一位細心的測試工程師，擅長發現問題並確保產品質量。"
        tools: ["browser"]
```

您可以根據需要自定義團隊名稱、描述、使用的模型和智能體組成。

## 可用工具列表

AgentMesh提供多種內置工具，可以在配置文件中為智能體分配：

- **calculator**：數學計算工具，支持複雜表達式求值
- **current_time**：當前時間獲取工具，解決模型時間感知問題
- **browser**：網頁瀏覽工具，支持網頁訪問、內容提取和交互
- **google_search**：搜索引擎工具，用於檢索最新信息
- **file_save**：將智能體輸出保存到本地工作區的工具
- **terminal**：命令行工具，用於安全執行系統命令

## 常見問題排解

### 1. 安裝依賴時出現錯誤

如果在安裝依賴時遇到錯誤，可以嘗試以下解決方案：

- 確保您的pip版本是最新的：`pip install --upgrade pip`
- 嘗試使用國內鏡像源：`pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple`
- 如果特定包安裝失敗，可以嘗試單獨安裝：`pip install package_name`

### 2. 瀏覽器工具無法使用

如果瀏覽器工具無法正常使用，請檢查：

- 確認您的Python版本是3.11+：`python --version`
- 確認已正確安裝browser-use和playwright：`pip list | grep browser-use`
- 嘗試重新安裝playwright：`playwright install`

### 3. 模型API調用失敗

如果模型API調用失敗，請檢查：

- 確認API密鑰已正確配置在`config.yaml`文件中
- 檢查網絡連接是否正常
- 確認API密鑰有足夠的配額和權限

### 4. Docker容器啟動失敗

如果Docker容器啟動失敗，請檢查：

- 確認Docker服務正在運行：`docker info`
- 檢查docker-compose.yml文件是否正確
- 嘗試重新下載docker-compose.yml文件

## 參考資料

1. AgentMesh GitHub倉庫：[https://github.com/MinimalFuture/AgentMesh](https://github.com/MinimalFuture/AgentMesh)
2. AgentMesh官方文檔：[https://docs.link-ai.tech/blog/agentmesh](https://docs.link-ai.tech/blog/agentmesh)
3. Python官網：[https://www.python.org/](https://www.python.org/)
4. Docker官網：[https://www.docker.com/](https://www.docker.com/)
