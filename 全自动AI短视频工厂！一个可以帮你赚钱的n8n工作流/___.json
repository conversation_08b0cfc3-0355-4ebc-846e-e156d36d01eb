{"name": "长视频", "nodes": [{"parameters": {"promptType": "define", "text": "=根据故事内容，分割为内容相关的n部分，n的值由你来定", "options": {"systemMessage": "=你是一个故事阅读者，根据故事内容，分为内容相关的n部分。n的值由你来定。\n\n要求：\n1. 每个部分不能少于8个字，不能多于20个字。\n2. 输出内容与原文一致，不能删减内容，不能添加内容，只做内容分为段落。\n3. 每个部分前面加上index.\n\n故事内容： \n{{ $('Clean Content').item.json.data }}\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [-400, 1440], "id": "09a69afb-79b4-4d94-a3f9-a948ac8b4ec6", "name": "AI Agent"}, {"parameters": {"method": "POST", "url": "=https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp-image-generation:generateContent?key=AIzaSyBStw-Hkc1PSe96-ef-ih4p3RQmrNSz2D4", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"contents\": [{\n      \"parts\": [\n        {\"text\": \"生成一张图片，通过这个意象{{ $json.message.content }}作为图片提示词。 这张图片是日式动漫风格，多彩，元素丰富，轮廓线条清晰，明暗对比强烈。图片比例必须是：16:9。必须先用文字回复。\"}\n      ]\n    }],\n    \"generationConfig\":{\"responseModalities\":[\"Text\",\"Image\"]}\n\n}\n\n", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [3020, 1040], "id": "dadc45dd-d341-4ad1-be58-2cbcc4c4b1c0", "name": "Gemini Image ", "retryOnFail": true, "onError": "continueRegularOutput"}, {"parameters": {"rule": {"interval": [{}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [1540, 2040], "id": "0f9e2972-e1b0-484e-9afa-17be8e285550", "name": "Schedule Trigger"}, {"parameters": {"rule": {"interval": [{}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [1540, 2480], "id": "cfebfa36-7c2f-4653-8497-1dafae165b35", "name": "Schedule Trigger1"}, {"parameters": {"aggregate": "aggregateAllItemData", "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [3000, 2500], "id": "47f40826-99b6-41b7-9aef-ef1c093ccf5f", "name": "Aggregate"}, {"parameters": {"rule": {"interval": [{}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [1560, 2960], "id": "9187d3d8-dc53-45be-9a55-12384205168e", "name": "Schedule Trigger2"}, {"parameters": {"assignments": {"assignments": [{"id": "f1de4220-e3ba-478a-9efb-7e425d2b1bfa", "name": "=data", "value": "={{ $json.directory }}/{{ $json.fileName }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2040, 2960], "id": "9c9f5590-8605-4165-a292-9ed45527b343", "name": "<PERSON>"}, {"parameters": {"content": "# 有声小说长视频的自动化工作流！\n——————————————————————————\n## 视频地址\nhttps://www.youtube.com/watch?v=FNUzlEY9DKE\n\n\n\n# Jay's AI Lab\n## 精选n8n工作流\n——————————————————————————\n### MCP AI 智能体工作流\nhttps://www.youtube.com/watch?v=h1nBLKsN6CA\n\n### RAG AI 智能知识库工作流\nhttps://www.youtube.com/watch?v=TKdxIZuHVEg\n\n### Crawl4AI 网站爬虫工作流\nhttps://www.youtube.com/watch?v=eZdnBTjiv04\n\n### ChatGPT 4o 图片生成功能用法脑图\nhttps://www.youtube.com/watch?v=7Io5Pe7cnbA\n\n### AI短视频生成工作流\nhttps://www.youtube.com/watch?v=f_jVxg3s0Kw\n\n### AI自动化生成上传短视频工作流(免费版）\nhttps://www.youtube.com/watch?v=vohYEGmLT5U\n\n### AI自动化新闻口播视频工作流\nhttps://www.youtube.com/watch?v=4CqcVmx713w\n ", "height": 900, "width": 900, "color": 5}, "type": "n8n-nodes-base.stickyNote", "position": [0, 1900], "typeVersion": 1, "id": "a3ee5dc1-7226-4a45-a839-be7c1f369715", "name": "Sticky Note5"}, {"parameters": {"fileSelector": "./Input/Book/demo.txt", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [300, 240], "id": "f90f9d59-eeae-4adc-bef5-e822800acc84", "name": "Read txt book from input folder"}, {"parameters": {"operation": "text", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [520, 240], "id": "3b40be25-03b6-4405-bac9-a6ee52c8c78b", "name": "Extract txt content"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [2040, 1060], "id": "313a82e6-95ba-400a-b03e-d5026358c1d8", "name": "Loop for Image"}, {"parameters": {}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [2340, 1060], "id": "459b08f1-a253-42e2-a946-b844bae19fb2", "name": "Wait for image", "webhookId": "ebd05ab3-b4d9-4cb9-948d-ff89ad4d5fb2"}, {"parameters": {"operation": "toBinary", "sourceProperty": "candidates[0].content.parts[1].inlineData.data", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [3320, 1080], "id": "ca550cde-c394-4fc1-9ec1-fc02a30fcd0c", "name": "Convert image", "alwaysOutputData": true, "retryOnFail": true, "onError": "continueRegularOutput"}, {"parameters": {"operation": "write", "fileName": "=./output/.image/A{{ $('Loop for Image').item.json.index }}.jpg", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [3540, 1080], "id": "d624f85b-eb19-4f0d-8d30-662557cb9edb", "name": "Store image to folder", "alwaysOutputData": false, "onError": "continueRegularOutput"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [2040, 1500], "id": "e6b5a097-e9ef-4150-b7f4-1ff97f246828", "name": "Loop for audio"}, {"parameters": {"method": "POST", "url": "http://host.docker.internal:7860/gradio_api/call/basic_tts", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"data\": [\n    {\n      \"meta\": { \"_type\": \"gradio.FileData\" },\n      \"path\": \"https://effjgjwbzmvdhfbazirw.supabase.co/storage/v1/object/public/n8n-audio//Male_Storyteller.MP3\"\n    },\n    \"\",\n    \"{{ $json.text }}\",\n    false,\n    true,\n    42,\n    150,\n    40,\n    1\n  ]\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2300, 1540], "id": "635f5cd2-3d89-40f8-8834-1db9c36838ed", "name": "Local F5-TTS"}, {"parameters": {}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [2520, 1540], "id": "1ec83e18-cca6-405b-b6f5-abc3aea18fc8", "name": "Wait for audio", "webhookId": "f1c7e165-0eaa-4561-87e4-e2cc9f5ce337"}, {"parameters": {"url": "=http://host.docker.internal:7860/gradio_api/call/switch_tts_model/{{ $json.event_id }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2760, 1540], "id": "78f474c7-09ef-4fdf-aa88-ff2c26466336", "name": "Get audio"}, {"parameters": {"fileSelector": "={{ $json.path}}", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [3380, 1540], "id": "daa5f723-9fed-4680-b859-99613f91c583", "name": "Get audio location"}, {"parameters": {"operation": "write", "fileName": "=./output/.audio/A{{ $('Loop for audio').item.json.index }}.mp3", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [3640, 1540], "id": "3030bafb-a7b0-4032-88af-1c47cc9ad5e3", "name": "Copy audio to folder"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [2040, 2040], "id": "e7a29243-4bb4-450b-a282-fcb9ca5f97d0", "name": "Loop for video"}, {"parameters": {"executeOnce": false, "command": "=ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 ./output/.audio/{{ $json.fileName }}"}, "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [2280, 2060], "id": "23a70399-3e0b-49ab-9dd9-51947fc6b882", "name": "Audio Length", "onError": "continueRegularOutput"}, {"parameters": {"fileSelector": "=./output/.image/{{ $('Loop for video').item.json.fileName.replace(\".mp3\",\"\") }}.jpg", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [2520, 2060], "id": "60b643c4-6991-41c8-a793-1866f5c2b7fb", "name": "Get image from folder", "alwaysOutputData": true, "onError": "continueRegularOutput"}, {"parameters": {"executeOnce": false, "command": "=ffmpeg -y \\\n-loop 1 \\\n-framerate 25 \\\n-i \"./output/.image/{{ $('Loop for video').item.json.fileName.replace(\".mp3\",\"\") }}.jpg\" \\\n-i \"./output/.audio/{{ $('Loop for video').item.json.fileName }}\" \\\n-filter_complex \"\n[0:v]scale=1920:1080:force_original_aspect_ratio=increase,crop=1920:1080,zoompan=z='zoom+0.0005':s=1920x1080:d=25*{{ $('Audio Length').item.json.stdout }}[v]\n\" \\\n-map \"[v]\" -map 1:a \\\n-c:v libx264 -preset veryfast -c:a aac -b:a 192k -pix_fmt yuv420p \\\n-shortest \\\n\"./output/.video/{{ $('Loop for video').item.json.fileName.replace(\".mp3\",\"\") }}.mp4\"\n\n"}, "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [2760, 2060], "id": "d1f45a3f-5985-4c1b-907b-f10ebb88bae8", "name": "Generate video segments", "onError": "continueRegularOutput"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [2040, 2480], "id": "0c2227ee-cde9-40c6-bc08-e7b8e83ee3fb", "name": "Loop for subtitle"}, {"parameters": {"method": "POST", "url": "https://speech.googleapis.com/v1/speech:recognize", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleApi", "sendBody": true, "contentType": "application/json", "bodyParameters": {"parameters": [{"name": "config", "value": {"encoding": "WEBM_OPUS", "sampleRateHertz": 48000, "languageCode": "zh-CN", "enableWordTimeOffsets": true, "enableAutomaticPunctuation": true, "model": "latest_long"}}, {"name": "audio", "value": {"content": "={{ $base64($binary.data) }}"}}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2280, 2500], "id": "google-speech-node", "name": "Google Speech to Text"}, {"parameters": {"jsCode": "const input = $input.all(); \nconst drawtexts = input.map(item => item.json.drawtext); \nconst filter_complex = drawtexts.join(',');\n\nconst index = $runIndex + 1; \nreturn [\n  {\n    json: {\n      index: index,\n      filter_complex: filter_complex\n    }\n  }\n];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2760, 2500], "id": "3fe081a4-0186-44fe-a351-e14063043a82", "name": "Group drawtext"}, {"parameters": {"executeOnce": false, "command": "=ffmpeg -y \\\n-i \"./output/.video/A{{ String($json.data[0].index).padStart(3, '0') }}.mp4\" \\\n-filter_complex \"\n[0:v]{{ $json.data[0].filter_complex }}[v0]\n\" \\\n-map \"[v0]\" \\\n-map 0:a? \\\n-c:v libx264 -preset veryfast -c:a copy -pix_fmt yuv420p \\\n\"./output/.final/F{{ String($json.data[0].index).padStart(3, '0') }}.mp4\"\n"}, "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [3240, 2500], "id": "4c64131d-854c-4041-8751-e18f0b95337f", "name": "Generate video with subtitle", "alwaysOutputData": true, "onError": "continueRegularOutput"}, {"parameters": {"fileSelector": "./output/.final/*", "options": {"fileExtension": ".mp4"}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [1800, 2960], "id": "79efcdfa-64a5-4e4a-bfe4-56f416ebff35", "name": "Get all video segments"}, {"parameters": {"jsCode": "const input = $input.all();\n\nconst fileList = input.map(item => {\n  const value = Object.values(item.json)[0]; \n  return `file '${value}'`;\n}).join('\\n');\n\nreturn [\n  {\n    json: {\n      fileListContent: fileList\n    }\n  }\n];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2280, 2960], "id": "cb8190bf-a6c5-45f4-90f6-b06cfd22127b", "name": "Video list"}, {"parameters": {"command": "=echo \"{{ $json.fileListContent }}\" > list.txt\n\nffmpeg -f concat -safe 0 -i list.txt -c copy ./output/.combined/output.mp4\n\n\n"}, "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [2520, 2960], "id": "3bf9917d-0d48-480e-af85-1eccdb6f2821", "name": "Combine videos"}, {"parameters": {"content": "# 小说内容整理，按章节储存\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n## 注意第一个节点输入章节分隔符\n## 比如每个章节开头为“第一章”，第一个节点就输入“章”。\n## 同样如果是类似”第00X卷“，就输入”卷“。\n\n\n\n\n\n\n\n", "height": 720, "width": 2120, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [-340, 40], "typeVersion": 1, "id": "d930efe2-afc5-4a92-bd9a-0b4c1073743f", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "# 生成并储存小说图片", "height": 400, "width": 1880}, "type": "n8n-nodes-base.stickyNote", "position": [1980, 940], "typeVersion": 1, "id": "deb4ee8a-3b2c-4226-80c5-6622edaef06b", "name": "Sticky Note1"}, {"parameters": {"content": "# 生成并储存小说朗读音频", "height": 460, "width": 1880}, "type": "n8n-nodes-base.stickyNote", "position": [1900, 1420], "typeVersion": 1, "id": "af620d1d-09df-4a1f-9f5e-a3a8c9c7db74", "name": "Sticky Note2"}, {"parameters": {"content": "# 生成并储存小说分段视频", "height": 380, "width": 1880}, "type": "n8n-nodes-base.stickyNote", "position": [1940, 1980], "typeVersion": 1, "id": "f42f37b7-c23b-4bce-80ac-61e8efc2cc5c", "name": "Sticky Note3"}, {"parameters": {"content": "# 小说分段视频加字幕", "height": 380, "width": 1880}, "type": "n8n-nodes-base.stickyNote", "position": [1980, 2400], "typeVersion": 1, "id": "c249e75b-8090-4f3f-8875-e82c83ae37d1", "name": "Sticky Note4"}, {"parameters": {"content": "# 合成最终视频", "height": 320, "width": 1880}, "type": "n8n-nodes-base.stickyNote", "position": [1980, 2880], "typeVersion": 1, "id": "948e73d5-08de-4c0c-bc17-aed45205a6d0", "name": "Sticky Note6"}, {"parameters": {"method": "PUT", "url": "={{ $('YouTube Placeholder').item.json.headers.location }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "youTubeOAuth2Api", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "video/webm"}]}, "sendBody": true, "contentType": "binaryData", "inputDataFieldName": "data", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [3400, 3400], "id": "13fb8b74-3ee4-4c08-99c2-3656389088a8", "name": "YouTube Upload", "credentials": {"youTubeOAuth2Api": {"id": "1lmPvt7xsQ1wojl3", "name": "YouTube account"}}}, {"parameters": {"method": "POST", "url": "=https://www.googleapis.com/upload/youtube/v3/videos?part=snippet,status&uploadType=resumable\n", "authentication": "predefinedCredentialType", "nodeCredentialType": "youTubeOAuth2Api", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "X-Upload-Content-Type", "value": "video/webm"}]}, "sendBody": true, "contentType": "raw", "rawContentType": "RAW/JSON", "body": "={\n  \"snippet\": {\n    \"title\": \"{{ $json.data2 }}\",\n    \"description\": \"{{ $('Extract description').item.json.data1 }}\",\n    \"defaultLanguage\": \"en\",\n    \"defaultAudioLanguage\": \"en\"\n  },\n  \"status\": {\n    \"privacyStatus\": \"public\",\n    \"license\": \"youtube\",\n    \"embeddable\": true,\n    \"publicStatsViewable\": true,\n    \"madeForKids\": false\n  }\n}\n", "options": {"response": {"response": {"fullResponse": true}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2940, 3400], "id": "b6eb6201-bde9-483c-9f23-253e950ddb01", "name": "YouTube Placeholder", "credentials": {"youTubeOAuth2Api": {"id": "1lmPvt7xsQ1wojl3", "name": "YouTube account"}}}, {"parameters": {"text": "={{ $json.data }}", "schemaType": "fromJson", "jsonSchemaExample": "{\n\t\"title\": \"《xxx》\"\n}", "options": {}}, "type": "@n8n/n8n-nodes-langchain.informationExtractor", "typeVersion": 1, "position": [740, 380], "id": "f8ee4f62-7313-4c21-82c0-fe05a10dd6fd", "name": "Title"}, {"parameters": {"operation": "write", "fileName": "./output/.txt/title.txt", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [1340, 380], "id": "1b865a98-82b1-4ba3-aa4f-9fd7f08914fe", "name": "Title file"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-280, 540], "id": "7f246272-d8d5-42f8-8cb0-3707c6108a84", "name": "OpenAI get title"}, {"parameters": {"promptType": "define", "text": "=更具小说内容，写一个100字以内的介绍。 小说内容：{{ $json.data }}", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [660, 1240], "id": "06bc661d-3b8f-462a-bbc5-c506359a71ce", "name": "AI Agent1"}, {"parameters": {"operation": "toText", "sourceProperty": "output.title", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [1120, 380], "id": "a13d25e5-bd71-4cf6-af7f-c6e793939cf2", "name": "Title to File"}, {"parameters": {"operation": "toText", "sourceProperty": "output", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [980, 1240], "id": "f1fdf2fd-56f2-43ec-9298-d5ee4f9944ef", "name": "Description to File"}, {"parameters": {"operation": "write", "fileName": "./output/.txt/description.txt", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [1160, 1240], "id": "8301194f-14c6-4937-8159-8fdf465d231a", "name": "Description file"}, {"parameters": {"rule": {"interval": [{}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [1560, 3400], "id": "53539aed-3710-4481-ba1c-23e4f84ed513", "name": "Schedule Trigger3"}, {"parameters": {"fileSelector": "./output/.txt/description.txt", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [2040, 3400], "id": "fd57e455-2c9a-4a26-8197-ff12e4a52ac9", "name": "Read description"}, {"parameters": {"operation": "text", "destinationKey": "data1", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [2260, 3400], "id": "2cc785fe-8307-4397-a1f0-55c490b755a1", "name": "Extract description"}, {"parameters": {"fileSelector": "./output/.txt/title.txt", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [2480, 3400], "id": "79d4d30e-d5e5-4004-8ffd-d9fa142b95a2", "name": "Read title"}, {"parameters": {"operation": "text", "destinationKey": "data2", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [2700, 3400], "id": "15348e0a-7905-4290-9bd6-037a52c434e2", "name": "Extract title"}, {"parameters": {"fileSelector": "=./output/.combined/output.mp4", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [3180, 3400], "id": "a742a97d-53e7-4a3e-a5a1-b5c38e9a097d", "name": "Read mp4"}, {"parameters": {"content": "# 上传到YouTube", "height": 320, "width": 1880}, "type": "n8n-nodes-base.stickyNote", "position": [1980, 3300], "typeVersion": 1, "id": "f1f30cb7-6b6c-42ad-a01a-0772a11e2689", "name": "Sticky Note8"}, {"parameters": {"operation": "toText", "sourceProperty": "content", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [1340, 20], "id": "f3e17cb2-b985-46e5-8e82-69732fe45211", "name": "Convert to File"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [1120, 80], "id": "aa770c92-f506-460d-9661-7852e229d62c", "name": "Loop Over Items"}, {"parameters": {"fileSelector": "=./output/.txt/Chapter {{ $json['Chapter Number'] }}.txt", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [300, 960], "id": "adc5006c-1b75-4ddf-b08f-b8093a44039b", "name": "Read txt chapter"}, {"parameters": {"operation": "text", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [540, 960], "id": "3e2d0805-d088-461c-a3b9-237cd5c880e9", "name": "Extract from File"}, {"parameters": {"operation": "write", "fileName": "=./output/.txt/{{ $('Loop Over Items').item.json.chapter }}.txt", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [1560, 80], "id": "b3f92fba-c096-44b2-b3d5-730651b546d6", "name": "Store Chapters"}, {"parameters": {"assignments": {"assignments": [{"id": "5e51be39-c452-4328-8c45-badc0203d716", "name": "=CharCount", "value": "={{  ($json[\"data\"] || '').match(/\\p{<PERSON><PERSON><PERSON>=Han}/gu)?.length || 0}}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1040, 960], "id": "4e19444e-8f55-46af-a671-e74439c101be", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"parameters": {"assignments": {"assignments": [{"id": "7cf7f418-a842-4856-8940-52b9730ab4e1", "name": "data", "value": "={{$json.data.replace(/^.*?\\r?\\n/, '').replace(/\\r?\\n/g, '')}}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [820, 960], "id": "ba11c7a9-5f35-4bd7-ae7e-12abec1a25ce", "name": "Clean Content"}, {"parameters": {"jsCode": "const raw = $input.first().json.output;\n\nconst re = /(\\d+)\\.\\s*([\\s\\S]*?)(?=\\n\\d+\\.\\s|$)/g;\n\nlet match;\nconst items = [];\n\nwhile ((match = re.exec(raw)) !== null) {\n  const index = String(match[1]).padStart(3, \"0\");\n  const text  = match[2].trim();      \n\n  items.push({ json: { index, text } });\n}\n\nreturn items;        "}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1660, 960], "id": "1926905e-ce2c-40ec-bfca-4417a9b583e1", "name": "Splitter"}, {"parameters": {"operation": "write", "fileName": "=./output/.segments/A{{ $('Loop for segment').item.json.index }}.txt", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [2500, 620], "id": "a087768e-08ad-4a72-be50-264054dd5a25", "name": "Store Segments to folder", "alwaysOutputData": false, "onError": "continueRegularOutput"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [2060, 600], "id": "52084397-2ead-42db-80e4-b7ec9c45db19", "name": "Loop for segment"}, {"parameters": {"operation": "write", "fileName": "=./output/.image/A{{ $('Missing segment').item.json['Segment Number'] }}.jpg", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [3600, 160], "id": "f9c95a16-80bd-472c-93f9-eee01f736427", "name": "Store single image", "alwaysOutputData": false, "onError": "continueRegularOutput"}, {"parameters": {"operation": "toBinary", "sourceProperty": "candidates[0].content.parts[1].inlineData.data", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [3360, 160], "id": "9d10424d-808f-43e3-afde-d1fcb298aff5", "name": "Convert single image", "alwaysOutputData": true, "retryOnFail": true, "onError": "continueRegularOutput"}, {"parameters": {"method": "POST", "url": "=https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp-image-generation:generateContent?key=AIzaSyBStw-Hkc1PSe96-ef-ih4p3RQmrNSz2D4", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"contents\": [{\n      \"parts\": [\n        {\"text\": \"生成一张图片，通过这个意象{{ $json.message.content }}作为图片提示词。 这张图片是日式动漫风格，多彩，元素丰富，轮廓线条清晰，明暗对比强烈。图片比例必须是：16:9。必须先用文字回复。\"}\n      ]\n    }],\n    \"generationConfig\":{\"responseModalities\":[\"Text\",\"Image\"]}\n\n}\n\n", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [3120, 160], "id": "d9c482ca-4fb3-4968-8655-46dae9b6a96d", "name": "Gemini single Image", "retryOnFail": true, "onError": "continueRegularOutput"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "GPT-4O-MINI"}, "messages": {"values": [{"content": "=你非常善于写图片提示词。\n\n写一个图片提示词，关于其中一小节内容：\n{{ $json.data }}\n\n只回复提示词，不加其他话。"}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [2740, 160], "id": "2f4f0b7c-94ce-4183-b306-67aa9f08cb05", "name": "OpenAI Single Image Prompt", "retryOnFail": true}, {"parameters": {"operation": "text", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [2500, 160], "id": "a8b45cca-8c41-475c-a229-3c51d5ba13b9", "name": "Segment text"}, {"parameters": {"fileSelector": "=./output/.segments/A{{ $json['Segment Number'] }}.txt", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [2280, 160], "id": "ecb0ebef-8b1c-4b42-b503-db61ee62cb6c", "name": "Read segment"}, {"parameters": {"formTitle": "Segment Number", "formFields": {"values": [{"fieldLabel": "Segment Number"}]}, "options": {}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [2060, 160], "id": "67f10cac-714d-4748-81fe-fefff124da61", "name": "Missing segment", "webhookId": "4f09c1fe-e2c2-4505-af1f-b59be8749d6a"}, {"parameters": {"operation": "toText", "sourceProperty": "text", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [2280, 620], "id": "cfe15bd3-349a-46cb-9e70-d566ec7b543f", "name": "Convert segment"}, {"parameters": {"content": "# 储存小说文字片段", "height": 400, "width": 1880}, "type": "n8n-nodes-base.stickyNote", "position": [1980, 460], "typeVersion": 1, "id": "8c8e0328-6c84-438b-ad2d-ad621e15c5ae", "name": "Sticky Note9"}, {"parameters": {"content": "# 备用：生成缺失图片", "height": 400, "width": 1880}, "type": "n8n-nodes-base.stickyNote", "position": [1980, 0], "typeVersion": 1, "id": "4fa378c1-6be6-4fc8-a6ee-d044edb12bf1", "name": "Sticky Note10"}, {"parameters": {"fileSelector": "./output/.audio/*", "options": {"fileExtension": ".mp3"}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [1780, 2040], "id": "ee9633d5-3624-4889-80ce-5e33ac6608b9", "name": "Get all audio"}, {"parameters": {"fileSelector": "./output/.audio/*", "options": {"fileExtension": ".mp3"}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [1780, 2480], "id": "798aefa1-83c7-4f39-ab1b-196897f0996b", "name": "Get all audio for subtitle"}, {"parameters": {"fileSelector": "./output/.txt/title.txt", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [300, 1540], "id": "bd504a3f-4460-4efe-8701-8c6f8bdc2ae0", "name": "Read number"}, {"parameters": {"operation": "text", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [520, 1540], "id": "12550e16-0085-4723-97df-a18be5a34bc6", "name": "Extract"}, {"parameters": {"assignments": {"assignments": [{"id": "61bf24de-9e32-4a77-8e80-998e487ac5b1", "name": "Title", "value": "={{ $json.data }} - 第{{ $('Chapter Number').item.json['Chapter Number'] }}章", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [740, 1540], "id": "4548054f-b04f-4a72-8777-ea1cb0a2d146", "name": "Edit Title"}, {"parameters": {"operation": "write", "fileName": "./output/.txt/title.txt", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [1240, 1540], "id": "4521a05d-890f-4ed0-a101-6f54520911c8", "name": "Title file1"}, {"parameters": {"operation": "toText", "sourceProperty": "Title", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [980, 1540], "id": "ce0b093b-f302-4192-8b10-ca198d9c716e", "name": "Title1"}, {"parameters": {"jsCode": "const input  = $input.first().json;\nconst words  = input.words;\nconst text   = input.text.trim();\n\nconst phrases = text.split(/\\s+/);\n\nfunction isPunc(ch) {\n  return /[\\s\\u3000\\uFF01-\\uFF5E\\u0021-\\u002F\\u003A-\\u0040\\u005B-\\u0060\\u007B-\\u007E，。！？、]/.test(ch);\n}\n\nlet groups = [];\nlet wordIndex = 0;\n\nfor (const phrase of phrases) {\n  const chars = [...phrase];           \n  let start = null;\n  let end   = null;\n\n  for (const ch of chars) {\n\n    while (wordIndex < words.length && words[wordIndex].word !== ch) {\n      if (!isPunc(words[wordIndex].word)) {\n\n        console.warn(`Mismatch: expected «${ch}» but saw «${words[wordIndex].word}»`);\n        break;\n      }\n      wordIndex++;  \n    }\n\n\n    if (wordIndex >= words.length) break;\n\n    const wObj = words[wordIndex];\n    if (start === null) start = wObj.start;\n    end = wObj.end;\n    wordIndex++;\n  }\n\n  if (start !== null) {\n    groups.push({ text: phrase, start, end });\n  }\n}\n\n// ---------- FFmpeg drawtext generation ----------\nconst fontfile   = './HanyiSentyMarshmallowChalkA.ttf';\nconst fontcolor  = 'yellow';\nconst fontsize   = 120;\nconst positionY  = 'h-150';\nconst borderw    = 4;\nconst bordercolor = 'black';\n\nconst output = groups.map(g => ({\n  json: {\n    drawtext:\n      `drawtext=text='${g.text.replace(/'/g, \"\\\\'\")}':` +\n      `fontfile='${fontfile}':fontcolor=${fontcolor}:fontsize=${fontsize}:` +\n      `borderw=${borderw}:bordercolor=${bordercolor}:x=(w-text_w)/2:` +\n      `y=${positionY}:enable='between(t,${g.start.toFixed(2)},${g.end.toFixed(2)})'`\n  }\n}));\n\nreturn output;\n\n\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2520, 2500], "id": "293290ad-3737-433e-8f49-d36121503227", "name": "Generate drawtext"}, {"parameters": {"formTitle": "Chapter Number", "formFields": {"values": [{"fieldLabel": "Chapter Number", "placeholder": "1"}]}, "options": {}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [-220, 1240], "id": "300a341d-6b23-4494-bab7-448a72048059", "name": "Chapter Number", "webhookId": "bd92f7b4-f5a0-4295-bdfd-a7efe68c5f5b"}, {"parameters": {"formTitle": "Splitter", "formFields": {"values": [{"fieldLabel": "Splitter"}]}, "options": {}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [80, 240], "id": "adcfc0e5-a29a-48fa-b2d5-0d88759a3290", "name": "Book Splitter", "webhookId": "335ae275-ab51-4766-a11b-8fdf4d468c86"}, {"parameters": {"modelName": "models/gemini-2.5-pro-preview-05-06", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [-220, 700], "id": "7e7fe7bd-0084-4fe5-8b5e-db171cb530b4", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "Fp7gYzwb1WHZdjXI", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatDeepSeek", "typeVersion": 1, "position": [840, 600], "id": "e770a1fb-e2d7-44b6-8d4e-7a4bb6d14f44", "name": "DeepSeek Chat Model", "credentials": {"deepSeekApi": {"id": "nFkzT1DCPXlBCmQT", "name": "DeepSeek account"}}}, {"parameters": {"modelName": "models/gemini-2.0-flash", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [640, 1400], "id": "70bbbde2-7834-4687-b10b-************", "name": "Google Gemini Chat Model1", "credentials": {"googlePalmApi": {"id": "Fp7gYzwb1WHZdjXI", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"modelName": "models/gemini-2.0-flash", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [-440, 1600], "id": "b021fc91-fcb0-4e52-8eee-7ebdae07f31f", "name": "Google Gemini Chat Model2", "credentials": {"googlePalmApi": {"id": "Fp7gYzwb1WHZdjXI", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"jsCode": "const rawText = $input.first().json.data;\n\nconst suffix = $node[\"Book Splitter\"].json.Splitter.trim() || \"卷\";\n\nfunction esc(str) { return str.replace(/[-/\\\\^$*+?.()|[\\]{}]/g, \"\\\\$&\"); }\nconst sufEsc = esc(suffix);\n\nfunction chineseToInt(cn) {\n  const map = { 零:0, 一:1, 二:2, 两:2, 三:3, 四:4, 五:5, 六:6,\n                七:7, 八:8, 九:9 };\n  if (cn === \"十\") return 10;\n  if (/^十[一二三四五六七八九]$/.test(cn))\n    return 10 + map[cn[1]];\n  if (/^[一二三四五六七八九]十$/.test(cn))\n    return map[cn[0]] * 10;\n  if (/^[一二三四五六七八九]十[一二三四五六七八九]$/.test(cn))\n    return map[cn[0]] * 10 + map[cn[2]];\n  return map[cn] ?? 0;        \n}\n\n\nconst splitRE = new RegExp(\n  `(?=` +                    \n  `第\\\\s*` +                 \n  `(?:[0-9０-９]+|[一二三四五六七八九十百千两]+)` + \n  `\\\\s*${sufEsc})`,           \n  \"g\"\n);\n\nconst numRE = new RegExp(\n  `第\\\\s*` +\n  `(?:` +\n    `([0-9０-９]+)` +      \n  `|` +\n    `([一二三四五六七八九十百千两]+)` + \n  `)` +\n  `\\\\s*${sufEsc}`\n);\n\nconst parts = rawText.split(splitRE).slice(1);\n\nreturn parts.map((section, idx) => {\n  const m = section.match(numRE);\n  let num = idx + 1;                       \n\n  if (m) {\n    if (m[1]) {                             \n      num = parseInt(\n        m[1].replace(/[０-９]/g, d => \"０１２３４５６７８９\".indexOf(d)),\n        10\n      );\n    } else if (m[2]) {                      \n      num = chineseToInt(m[2]);\n    }\n  }\n\n  return {\n    json: {\n      chapter : `Chapter ${num}`,\n      content : section.trim()\n    }\n  };\n});\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [820, 80], "id": "0356defa-a0af-4ee0-8c5e-72ff6dfadaf7", "name": "Split by chapters"}, {"parameters": {"promptType": "define", "text": "你非常善于写图片生成提示词。\\n\\n写一个生成图片的提示词，关于其中一小节内容：\\n{{ $json.data }}\\n\\n只回复提示词，不加其他话", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [2680, 1040], "id": "c5d60e9e-9499-403d-ad02-41a8e1337b4c", "name": "图画提示词"}, {"parameters": {"modelName": "models/gemini-2.5-pro-preview-05-06", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [2480, 1240], "id": "ebb80009-4781-4b55-90a3-7bc0f3ecf588", "name": "Google Gemini Chat Model4", "credentials": {"googlePalmApi": {"id": "Fp7gYzwb1WHZdjXI", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"jsCode": "// 获取输入数据\nconst inputData = $input.all()[0].json.data;\n\n// 解析data字段中的JSON字符串\n// 先去掉 \"event: complete\\ndata: \" 前缀\nconst jsonStart = inputData.indexOf('[');\nconst jsonData = inputData.substring(jsonStart);\n// 去掉末尾的额外数据\nconst jsonEnd = jsonData.lastIndexOf(']') + 1;\nconst cleanJsonData = jsonData.substring(0, jsonEnd);\n\n// 解析JSON数组\nconst parsedData = JSON.parse(cleanJsonData);\n\n// 提取音频文件信息\nconst audioFile = parsedData.find(item => \n  item.path && item.path.includes('.wav')\n);\n\nreturn [{\n  json: {\n    path: audioFile.path,\n    originalName: audioFile.orig_name\n  }\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [3060, 1540], "id": "1e94c8b2-1faf-4bc4-9ee3-a2250db12510", "name": "Code"}, {"parameters": {"content": "# 提取小说章节，按语义分块", "height": 960, "width": 1800, "color": 6}, "type": "n8n-nodes-base.stickyNote", "position": [-20, 820], "typeVersion": 1, "id": "baaa1fb9-0fda-49e8-88d9-7b723c96682a", "name": "Sticky Note7"}, {"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-200, 960], "id": "8cad3826-0f8b-462c-8941-12b3e3c95006", "name": "When clicking ‘Execute workflow’"}, {"parameters": {"assignments": {"assignments": [{"id": "f4e7eb79-bb38-4bbc-99dc-a1f3897da93b", "name": "Chapter Number", "value": "1", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [40, 960], "id": "1f61677c-910b-4b9f-b650-4fd91a0a8be5", "name": "自动设置章节"}, {"parameters": {"jsCode": "// 获取故事内容\nconst storyContent = $('Clean Content').item.json.data;\n\n// 如果没有获取到内容，返回错误\nif (!storyContent) {\n  return [{\n    json: {\n      error: \"未能获取到故事内容\"\n    }\n  }];\n}\n\n// 优化的分段函数 - 严格按强标点断句优先\nfunction segmentStory(text) {\n  let segments = [];\n  \n  // 清理文本，去除多余空格和换行\n  text = text.replace(/\\s+/g, '').trim();\n  \n  // 第一步：严格按强标点符号（。！？）分割\n  // 使用正则表达式匹配强标点，并保留标点符号\n  const strongPunctuationRegex = /([。！？])/;\n  const parts = text.split(strongPunctuationRegex);\n  \n  let currentSentence = '';\n  \n  for (let i = 0; i < parts.length; i++) {\n    const part = parts[i];\n    \n    if (!part) continue;\n    \n    // 如果是强标点符号\n    if (strongPunctuationRegex.test(part)) {\n      currentSentence += part;\n      // 遇到强标点就断句，这是最高优先级\n      if (currentSentence.trim()) {\n        segments.push(currentSentence.trim());\n        currentSentence = '';\n      }\n    } else {\n      // 如果是文本内容，直接拼接\n      currentSentence += part;\n    }\n  }\n  \n  // 处理最后没有强标点的剩余内容\n  if (currentSentence.trim()) {\n    segments.push(currentSentence.trim());\n  }\n  \n  // 第二步：处理短句（少于8字的句子）\n  const processedSegments = [];\n  \n  for (let i = 0; i < segments.length; i++) {\n    const current = segments[i];\n    \n    // 如果当前句子少于8字，尝试与后面的句子合并\n    if (current.length < 8) {\n      // 优先与后面的句子合并\n      if (i + 1 < segments.length) {\n        segments[i + 1] = current + segments[i + 1];\n        continue; // 跳过当前句子，因为已经合并到下一句了\n      }\n      // 如果没有后面的句子，与前面的句子合并\n      else if (processedSegments.length > 0) {\n        processedSegments[processedSegments.length - 1] += current;\n        continue;\n      }\n    }\n    \n    // 正常长度的句子直接添加\n    processedSegments.push(current);\n  }\n  \n  // 第三步：添加序号并返回\n  const finalResult = processedSegments\n    .filter(segment => segment.trim()) // 过滤空字符串\n    .map((segment, index) => `${index + 1}. ${segment}`)\n    .join('\\n');\n  \n  return finalResult;\n}\n\n// 执行分段\nconst result = segmentStory(storyContent);\n\n// 返回结果\nreturn [{\n  json: {\n    output: result\n  }\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1260, 960], "id": "839458c5-da82-4b8f-972c-7a2c1cc5fedf", "name": "Code1"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatDeepSeek", "typeVersion": 1, "position": [2660, 1220], "id": "ad4f4c87-a0c2-4834-b2c7-e3f30f247443", "name": "DeepSeek Chat Model1", "credentials": {"deepSeekApi": {"id": "nFkzT1DCPXlBCmQT", "name": "DeepSeek account"}}}], "pinData": {}, "connections": {"AI Agent": {"main": [[]]}, "Gemini Image ": {"main": [[{"node": "Convert image", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Get all audio", "type": "main", "index": 0}]]}, "Schedule Trigger1": {"main": [[{"node": "Get all audio for subtitle", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "Generate video with subtitle", "type": "main", "index": 0}]]}, "Schedule Trigger2": {"main": [[{"node": "Get all video segments", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Video list", "type": "main", "index": 0}]]}, "Read txt book from input folder": {"main": [[{"node": "Extract txt content", "type": "main", "index": 0}]]}, "Extract txt content": {"main": [[{"node": "Title", "type": "main", "index": 0}, {"node": "Split by chapters", "type": "main", "index": 0}]]}, "Loop for Image": {"main": [[], [{"node": "Wait for image", "type": "main", "index": 0}]]}, "Wait for image": {"main": [[{"node": "图画提示词", "type": "main", "index": 0}]]}, "Convert image": {"main": [[{"node": "Store image to folder", "type": "main", "index": 0}]]}, "Store image to folder": {"main": [[{"node": "Loop for Image", "type": "main", "index": 0}]]}, "Loop for audio": {"main": [[], [{"node": "Local F5-TTS", "type": "main", "index": 0}]]}, "Local F5-TTS": {"main": [[{"node": "Wait for audio", "type": "main", "index": 0}]]}, "Wait for audio": {"main": [[{"node": "Get audio", "type": "main", "index": 0}]]}, "Get audio": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Get audio location": {"main": [[{"node": "Copy audio to folder", "type": "main", "index": 0}, {"node": "Loop for audio", "type": "main", "index": 0}]]}, "Copy audio to folder": {"main": [[]]}, "Loop for video": {"main": [[], [{"node": "Audio Length", "type": "main", "index": 0}]]}, "Audio Length": {"main": [[{"node": "Get image from folder", "type": "main", "index": 0}]]}, "Get image from folder": {"main": [[{"node": "Generate video segments", "type": "main", "index": 0}]]}, "Generate video segments": {"main": [[{"node": "Loop for video", "type": "main", "index": 0}]]}, "Loop for subtitle": {"main": [[], [{"node": "Subtitle with timestamp", "type": "main", "index": 0}]]}, "Subtitle with timestamp": {"main": [[{"node": "Generate drawtext", "type": "main", "index": 0}]]}, "Group drawtext": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Generate video with subtitle": {"main": [[{"node": "Loop for subtitle", "type": "main", "index": 0}]]}, "Get all video segments": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Video list": {"main": [[{"node": "Combine videos", "type": "main", "index": 0}]]}, "YouTube Placeholder": {"main": [[{"node": "Read mp4", "type": "main", "index": 0}]]}, "Title": {"main": [[{"node": "Title to File", "type": "main", "index": 0}]]}, "OpenAI get title": {"ai_languageModel": [[]]}, "Title to File": {"main": [[{"node": "Title file", "type": "main", "index": 0}]]}, "AI Agent1": {"main": [[{"node": "Description to File", "type": "main", "index": 0}]]}, "Description to File": {"main": [[{"node": "Description file", "type": "main", "index": 0}]]}, "Schedule Trigger3": {"main": [[{"node": "Read description", "type": "main", "index": 0}]]}, "Read description": {"main": [[{"node": "Extract description", "type": "main", "index": 0}]]}, "Extract description": {"main": [[{"node": "Read title", "type": "main", "index": 0}]]}, "Read title": {"main": [[{"node": "Extract title", "type": "main", "index": 0}]]}, "Extract title": {"main": [[{"node": "YouTube Placeholder", "type": "main", "index": 0}]]}, "Read mp4": {"main": [[{"node": "YouTube Upload", "type": "main", "index": 0}]]}, "Convert to File": {"main": [[{"node": "Store Chapters", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Convert to File", "type": "main", "index": 0}]]}, "Read txt chapter": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "Clean Content", "type": "main", "index": 0}, {"node": "AI Agent1", "type": "main", "index": 0}]]}, "Store Chapters": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "CharCount": {"main": [[{"node": "Code1", "type": "main", "index": 0}]]}, "Clean Content": {"main": [[{"node": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Splitter": {"main": [[{"node": "Loop for audio", "type": "main", "index": 0}, {"node": "Loop for segment", "type": "main", "index": 0}, {"node": "Loop for Image", "type": "main", "index": 0}]]}, "Store Segments to folder": {"main": [[{"node": "Loop for segment", "type": "main", "index": 0}]]}, "Loop for segment": {"main": [[], [{"node": "Convert segment", "type": "main", "index": 0}]]}, "Convert single image": {"main": [[{"node": "Store single image", "type": "main", "index": 0}]]}, "Gemini single Image": {"main": [[{"node": "Convert single image", "type": "main", "index": 0}]]}, "OpenAI Single Image Prompt": {"main": [[{"node": "Gemini single Image", "type": "main", "index": 0}]]}, "Segment text": {"main": [[{"node": "OpenAI Single Image Prompt", "type": "main", "index": 0}]]}, "Read segment": {"main": [[{"node": "Segment text", "type": "main", "index": 0}]]}, "Missing segment": {"main": [[{"node": "Read segment", "type": "main", "index": 0}]]}, "Convert segment": {"main": [[{"node": "Store Segments to folder", "type": "main", "index": 0}]]}, "Get all audio": {"main": [[{"node": "Loop for video", "type": "main", "index": 0}]]}, "Get all audio for subtitle": {"main": [[{"node": "Loop for subtitle", "type": "main", "index": 0}]]}, "Read number": {"main": [[{"node": "Extract", "type": "main", "index": 0}]]}, "Extract": {"main": [[{"node": "Edit Title", "type": "main", "index": 0}]]}, "Edit Title": {"main": [[{"node": "Title1", "type": "main", "index": 0}]]}, "Title1": {"main": [[{"node": "Title file1", "type": "main", "index": 0}]]}, "Generate drawtext": {"main": [[{"node": "Group drawtext", "type": "main", "index": 0}]]}, "Chapter Number": {"main": [[]]}, "Book Splitter": {"main": [[{"node": "Read txt book from input folder", "type": "main", "index": 0}]]}, "Split by chapters": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[]]}, "DeepSeek Chat Model": {"ai_languageModel": [[{"node": "Title", "type": "ai_languageModel", "index": 0}]]}, "Google Gemini Chat Model1": {"ai_languageModel": [[{"node": "AI Agent1", "type": "ai_languageModel", "index": 0}]]}, "Google Gemini Chat Model2": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "图画提示词": {"main": [[{"node": "Gemini Image ", "type": "main", "index": 0}]]}, "Google Gemini Chat Model4": {"ai_languageModel": [[]]}, "Code": {"main": [[{"node": "Get audio location", "type": "main", "index": 0}]]}, "When clicking ‘Execute workflow’": {"main": [[{"node": "自动设置章节", "type": "main", "index": 0}]]}, "自动设置章节": {"main": [[{"node": "Read txt chapter", "type": "main", "index": 0}]]}, "Code1": {"main": [[{"node": "Splitter", "type": "main", "index": 0}]]}, "DeepSeek Chat Model1": {"ai_languageModel": [[{"node": "图画提示词", "type": "ai_languageModel", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "3068f8d8-9b5f-4487-941e-cb53776edaab", "meta": {"instanceId": "d77e7e872e8a69a95339451e9d8a5d0c176b3163d07fbc7d243a8036e5d030c9"}, "id": "DJejiA7OFzYYManv", "tags": []}