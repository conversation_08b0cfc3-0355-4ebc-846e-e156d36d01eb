// 获取故事内容
const storyContent = $('Clean Content').item.json.data;

// 如果没有获取到内容，返回错误
if (!storyContent) {
  return [{
    json: {
      error: "未能获取到故事内容"
    }
  }];
}

/**
 * 优化的中文文本智能分段函数
 * 规则：
 * 1. 强标点断句 (。！？) - 优先级最高，强制断句
 * 2. 智能长度控制 - 35字软限制，超长句子在次级标点处分割 (，；：)
 * 3. 短句合并 - 小于5字的句子自动合并
 */
function segmentStory(text) {
  // 清理文本，去除多余空格和换行
  text = text.replace(/\s+/g, '').trim();
  
  if (!text) return '';
  
  // 第一步：按强标点分割
  const primarySegments = splitByStrongPunctuation(text);
  
  // 第二步：处理超长句子
  const processedSegments = processLongSentences(primarySegments);
  
  // 第三步：合并短句
  const mergedSegments = mergeShortSentences(processedSegments);
  
  // 第四步：格式化输出
  return formatOutput(mergedSegments);
}

/**
 * 按强标点符号分割文本 (。！？)
 */
function splitByStrongPunctuation(text) {
  const strongPunctuations = /([。！？])/;
  const parts = text.split(strongPunctuations);
  const segments = [];
  let currentSentence = '';
  
  for (const part of parts) {
    if (!part) continue;
    
    if (strongPunctuations.test(part)) {
      currentSentence += part;
      if (currentSentence.trim()) {
        segments.push(currentSentence.trim());
        currentSentence = '';
      }
    } else {
      currentSentence += part;
    }
  }
  
  // 处理没有结束标点的剩余内容
  if (currentSentence.trim()) {
    segments.push(currentSentence.trim());
  }
  
  return segments;
}

/**
 * 处理超长句子（超过35字）
 */
function processLongSentences(segments) {
  const processedSegments = [];
  
  for (const segment of segments) {
    if (segment.length <= 35) {
      processedSegments.push(segment);
    } else {
      // 按次要标点符号分割长句
      const subSegments = splitBySecondaryPunctuation(segment);
      processedSegments.push(...subSegments);
    }
  }
  
  return processedSegments;
}

/**
 * 按次要标点符号分割长句 (，；：)
 */
function splitBySecondaryPunctuation(longSentence) {
  const secondaryPunctuations = /([，；：])/;
  const parts = longSentence.split(secondaryPunctuations);
  const segments = [];
  let currentPart = '';
  
  for (const part of parts) {
    if (!part) continue;
    
    if (secondaryPunctuations.test(part)) {
      currentPart += part;
      // 如果当前部分长度合适，作为一个分段
      if (currentPart.length >= 8 && currentPart.length <= 35) {
        segments.push(currentPart);
        currentPart = '';
      }
    } else {
      // 检查添加当前部分后是否超长
      if ((currentPart + part).length <= 35) {
        currentPart += part;
      } else {
        // 如果当前部分不为空，先保存
        if (currentPart.trim()) {
          segments.push(currentPart);
        }
        currentPart = part;
      }
    }
  }
  
  // 处理剩余部分
  if (currentPart.trim()) {
    if (currentPart.length <= 35) {
      segments.push(currentPart);
    } else {
      // 强制按35字分割
      segments.push(...forceChunkText(currentPart, 35));
    }
  }
  
  return segments.filter(seg => seg.trim());
}

/**
 * 强制按指定长度分割文本
 */
function forceChunkText(text, maxLength) {
  const chunks = [];
  let start = 0;
  
  while (start < text.length) {
    const chunk = text.substring(start, start + maxLength);
    chunks.push(chunk);
    start += maxLength;
  }
  
  return chunks;
}

/**
 * 合并短句（小于5字）
 * 合并逻辑：前面 → 后面 → 保持原样
 */
function mergeShortSentences(segments) {
  const mergedSegments = [];
  
  for (let i = 0; i < segments.length; i++) {
    const segment = segments[i].trim();
    
    if (segment.length < 5) {
      let merged = false;
      
      // 尝试与前一个句子合并
      if (mergedSegments.length > 0) {
        const prevSegment = mergedSegments[mergedSegments.length - 1];
        if ((prevSegment + segment).length <= 35) {
          mergedSegments[mergedSegments.length - 1] = prevSegment + segment;
          merged = true;
        }
      }
      
      // 如果无法与前一个合并，尝试与后一个合并
      if (!merged && i + 1 < segments.length) {
        const nextSegment = segments[i + 1];
        if ((segment + nextSegment).length <= 35) {
          segments[i + 1] = segment + nextSegment;
          merged = true;
        }
      }
      
      // 如果都无法合并，保持原样
      if (!merged) {
        mergedSegments.push(segment);
      }
    } else {
      mergedSegments.push(segment);
    }
  }
  
  return mergedSegments;
}

/**
 * 格式化输出为带序号的格式
 */
function formatOutput(segments) {
  const validSegments = segments.filter(segment => segment.trim());
  const numberedSegments = validSegments.map((segment, index) => 
    `${index + 1}. ${segment.trim()}`
  );
  
  return numberedSegments.join('\n');
}

// 执行分段处理
const result = segmentStory(storyContent);

// 返回结果
return [{
  json: {
    output: result
  }
}];
